

# ===============================
# Title: Fuzzy Deep Hybrid Network (FDHN) for Fake News Detection
# Author: <PERSON>
# Create Date: Apr 21, 2023
# Revision Date: Otc 23, 2023
# Github: https://github.com/chengxuphd/FDHN
# DOI: https://doi.org/10.1145/3628797.3628971
# Description: This is the source code of FDHN model. The dataset used in this code is LIAR (https://www.cs.ucsb.edu/~william/data/liar_dataset.zip).
# Notes: This is code for the TC+TC+CB+FZ experiment.
# ===============================

import pandas as pd
import numpy as np
import time
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.utils.data as data
from sklearn.metrics import f1_score
from torch.utils.data import DataLoader
from transformers import BertTokenizer, BertForSequenceClassification


# Fixing the randomness of CUDA.
torch.backends.cudnn.deterministic = True
torch.backends.cudnn.benchmark = False

np.random.seed(42)
torch.manual_seed(42)

DEVICE = torch.device("cuda") if torch.cuda.is_available() else torch.device("cpu")
print("PyTorch Version : {}".format(torch.__version__))
print(DEVICE)


#worksapce = 'D:/code/HLAN/FAKE/liar_dataset/'
worksapce = 'E:/liuhui/ljl/FAKE/FAKE/liar_dataset/'
model_save = 'TC+TC+CB+FZ.pt'
model_name = 'TC+TC+CB+FZ'
num_epochs = 10
batch_size = 32
learning_rate = 1e-3
num_classes = 6
padding_idx = 0
metadata_each_dim = 10


col = ['id', 'label', 'statement', 'subject', 'speaker', 'job_title', 'state_info', 'party_affiliation', 'barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts', 'context']

label_map = {0: 'pants-fire', 1: 'false', 2: 'barely-true', 3: 'half-true', 4: 'mostly-true', 5: 'true'}
label_convert = {'pants-fire': 0, 'false': 1, 'barely-true': 2, 'half-true': 3, 'mostly-true': 4, 'true':5}

train_data = pd.read_csv(worksapce + 'train.tsv', sep = '\t', names = col)
test_data = pd.read_csv(worksapce + 'test.tsv', sep = '\t', names = col)
val_data = pd.read_csv(worksapce + 'valid.tsv', sep = '\t', names = col)

# Replace NaN values with 'NaN'
train_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = train_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)
train_data.fillna('NaN', inplace=True)

test_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = test_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)
test_data.fillna('NaN', inplace=True)

val_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = val_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)
val_data.fillna('NaN', inplace=True)




# def textProcess(input_text, max_length = -1):
#     tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
#     if max_length == -1:
#         tokens = tokenizer(input_text, truncation=True, padding=True)
#     else:
#         tokens = tokenizer(input_text, truncation=True, padding='max_length', max_length=max_length)
#     return tokens

def textProcess(input_text, max_length = -1):
    local_model_path = 'E:/liuhui/ljl/FAKE/FAKE/bert-base-uncased'
    tokenizer = BertTokenizer.from_pretrained(local_model_path, local_files_only=True)
    
    if max_length == -1:
        tokens = tokenizer(input_text, truncation=True, padding=True)
    else:
        tokens = tokenizer(input_text, truncation=True, padding='max_length', max_length=max_length)
    return tokens

# Define a custom dataset for loading the data
class LiarDataset(data.Dataset):
    def __init__(self, data_df, statement, label_onehot, label, subject, speaker, job_title, state_info,
                     party_affiliation, barely_true_counts, false_counts, half_true_counts, mostly_true_counts,
                    pants_on_fire_counts, context):
        self.data_df = data_df
        self.statement = statement
        self.label_onehot = label_onehot
        self.label = label
        self.metadata_text = torch.cat((subject.int(), speaker.int(), job_title.int(), state_info.int(), party_affiliation.int(), 
                                   context.int()), dim=-1)
        self.metadata_number = torch.cat((torch.tensor(barely_true_counts, dtype=torch.float).unsqueeze(1), torch.tensor(false_counts, dtype=torch.float).unsqueeze(1), 
                                   torch.tensor(half_true_counts, dtype=torch.float).unsqueeze(1), torch.tensor(mostly_true_counts, dtype=torch.float).unsqueeze(1), 
                                   torch.tensor(pants_on_fire_counts, dtype=torch.float).unsqueeze(1)), dim=-1)


        
    def __len__(self):
        return len(self.data_df)
    
    def __getitem__(self, idx):
        statement = self.statement[idx]
        label_onehot = self.label_onehot[idx]
        label = self.label[idx]
        metadata_text = self.metadata_text[idx]
        metadata_number = self.metadata_number[idx]
        return statement, label_onehot, label, metadata_text, metadata_number


# Define the data loaders for training and validation
train_text = torch.tensor(textProcess(train_data['statement'].tolist())['input_ids'])
train_label = torch.nn.functional.one_hot(torch.tensor(train_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)
train_subject = torch.tensor(textProcess(train_data['subject'].tolist(), metadata_each_dim)['input_ids'])
train_speaker = torch.tensor(textProcess(train_data['speaker'].tolist(), metadata_each_dim)['input_ids'])
train_job_title = torch.tensor(textProcess(train_data['job_title'].tolist(), metadata_each_dim)['input_ids'])
train_state_info = torch.tensor(textProcess(train_data['state_info'].tolist(), metadata_each_dim)['input_ids'])
train_party_affiliation = torch.tensor(textProcess(train_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])
train_context = torch.tensor(textProcess(train_data['context'].tolist(), metadata_each_dim)['input_ids'])

train_dataset = LiarDataset(train_data, train_text, train_label, torch.tensor(train_data['label'].replace(label_convert)), 
                            train_subject, train_speaker, train_job_title, 
                            train_state_info, train_party_affiliation, 
                            train_data['barely_true_counts'].tolist(), train_data['false_counts'].tolist(), 
                            train_data['half_true_counts'].tolist(), train_data['mostly_true_counts'].tolist(), 
                            train_data['pants_on_fire_counts'].tolist(), train_context)
train_loader = data.DataLoader(train_dataset, batch_size=batch_size, shuffle=True)

val_text = torch.tensor(textProcess(val_data['statement'].tolist())['input_ids'])
val_label = torch.nn.functional.one_hot(torch.tensor(val_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)
val_subject = torch.tensor(textProcess(val_data['subject'].tolist(), metadata_each_dim)['input_ids'])
val_speaker = torch.tensor(textProcess(val_data['speaker'].tolist(), metadata_each_dim)['input_ids'])
val_job_title = torch.tensor(textProcess(val_data['job_title'].tolist(), metadata_each_dim)['input_ids'])
val_state_info = torch.tensor(textProcess(val_data['state_info'].tolist(), metadata_each_dim)['input_ids'])
val_party_affiliation = torch.tensor(textProcess(val_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])
val_context = torch.tensor(textProcess(val_data['context'].tolist(), metadata_each_dim)['input_ids'])

val_dataset = LiarDataset(val_data, val_text, val_label, torch.tensor(val_data['label'].replace(label_convert)),
                          val_subject, val_speaker, val_job_title, 
                          val_state_info, val_party_affiliation, 
                          val_data['barely_true_counts'].tolist(), val_data['false_counts'].tolist(), 
                          val_data['half_true_counts'].tolist(), val_data['mostly_true_counts'].tolist(), 
                          val_data['pants_on_fire_counts'].tolist(), val_context)
val_loader = data.DataLoader(val_dataset, batch_size=batch_size)

test_text = torch.tensor(textProcess(test_data['statement'].tolist())['input_ids'])
test_label = torch.nn.functional.one_hot(torch.tensor(test_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)
test_subject = torch.tensor(textProcess(test_data['subject'].tolist(), metadata_each_dim)['input_ids'])
test_speaker = torch.tensor(textProcess(test_data['speaker'].tolist(), metadata_each_dim)['input_ids'])
test_job_title = torch.tensor(textProcess(test_data['job_title'].tolist(), metadata_each_dim)['input_ids'])
test_state_info = torch.tensor(textProcess(test_data['state_info'].tolist(), metadata_each_dim)['input_ids'])
test_party_affiliation = torch.tensor(textProcess(test_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])
test_context = torch.tensor(textProcess(test_data['context'].tolist(), metadata_each_dim)['input_ids'])

test_dataset = LiarDataset(test_data, test_text, test_label, torch.tensor(test_data['label'].replace(label_convert)),
                          test_subject, test_speaker, test_job_title, 
                          test_state_info, test_party_affiliation, 
                          test_data['barely_true_counts'].tolist(), test_data['false_counts'].tolist(), 
                          test_data['half_true_counts'].tolist(), test_data['mostly_true_counts'].tolist(), 
                          test_data['pants_on_fire_counts'].tolist(), test_context)
test_loader = data.DataLoader(test_dataset, batch_size=batch_size)






from torch.autograd import Variable
import math
class ConvLayer(nn.Module):
    """ N-gram convolutional layer
    
        Args:
            in_channels: convolutional input channels
            out_channels: convolutional output channels
            kernel_size: convolutional kernel size
            stride: convolutional stride
    
    """
    def __init__(self, in_channels, out_channels, kernel_size, stride):
        super(ConvLayer, self).__init__()

        self.convnet = nn.Sequential(
            nn.Conv1d(
                in_channels=in_channels, 
                out_channels=out_channels, 
                kernel_size=kernel_size,
                stride=stride
            ),
            nn.ELU(),
        )

    def forward(self, x):
        return self.convnet(x)
    
def calculate_conv_output(input_, kernel, padding, stride):
    """Calculate the output size in Convolution layer"""
    return math.floor(((input_ - kernel + 2 * padding) / stride) + 1)
class PrimaryCaps(nn.Module):
    """ Primary caps layer
    
    
        Args:
            num_capsules: capsules count
            in_channels: input channels
            out_channels: output channels
            kernel_size: capsule kernel size
            stride: capsule stride
            conv_out_size: tensor size from above layer

    """
    def __init__(self, num_capsules, in_channels, out_channels, kernel_size, stride, conv_out_size):
        super(PrimaryCaps, self).__init__()

        self.out_channels = out_channels
        self.capsules = nn.ModuleList([
            nn.Conv1d(
                in_channels=in_channels, 
                out_channels=out_channels, 
                kernel_size=kernel_size, 
                stride=stride, 
                padding=0
            ) 
            for _ in range(num_capsules)
        ])
        
        self._out_channels = out_channels
        self._conv_out_size = conv_out_size
    
    def forward(self, x):
        u = [capsule(x) for capsule in self.capsules]
        u = torch.stack(u, dim=1)
        u = u.view(x.size(0), self._out_channels * self._conv_out_size , -1)

        return self.squash(u)
    
    def squash(self, input_tensor):
        squared_norm = (input_tensor ** 2).sum(-1, keepdim=True)
        output_tensor = squared_norm *  input_tensor / ((1. + squared_norm) * torch.sqrt(squared_norm + 1e-07))

        return output_tensor

class SecondaryCaps(nn.Module):
    """ Secondary capsules layer
    
        Args:
            num_capsules: capsules count
            num_routes: routing iteration count
            in_channels: in channels dim
            out_channels: out channels dim
    
    """
    def __init__(self, num_capsules, num_routes, in_channels, out_channels):
        super(SecondaryCaps, self).__init__()

        self.in_channels = in_channels
        self.num_routes = num_routes
        self.num_capsules = num_capsules

        self.W = nn.Parameter(torch.randn(1, num_routes, num_capsules, out_channels, in_channels))

    def forward(self, x):
        batch_size = x.size(0)
        x = torch.stack([x] * self.num_capsules, dim=2).unsqueeze(4)

        W = torch.cat([self.W] * batch_size, dim=0)
        # print(x.shape, W.shape)
        u_hat = torch.matmul(W, x)

        b_ij = Variable(torch.zeros(1, self.num_routes, self.num_capsules, 1)).to(cuda)

        num_iterations = 3
        for iteration in range(num_iterations):
            c_ij = F.softmax(b_ij, dim=2)

            c_ij = torch.cat([c_ij] * batch_size, dim=0).unsqueeze(4)

            s_j = (c_ij * u_hat).sum(dim=1, keepdim=True)
            v_j = self.squash(s_j)
            
            if iteration < num_iterations - 1:
                a_ij = torch.matmul(u_hat.transpose(3, 4), torch.cat([v_j] * self.num_routes, dim=1))
                b_ij = b_ij + a_ij.squeeze(4).mean(dim=0, keepdim=True)

        return v_j.squeeze(1)
    
    def squash(self, input_tensor):
        squared_norm = (input_tensor ** 2).sum(-1, keepdim=True)
        output_tensor = squared_norm *  input_tensor / ((1. + squared_norm) * torch.sqrt(squared_norm + 1e-07))

        return output_tensor
    
class CapsNet(nn.Module):
    """ Caps net module of CBAE
    
        Args:
            conv_out_ch: N-gram convolutional out channels
            conv_kernel: N-gram convolutional kernel size
            conv_stride: N-gram convolutional stride
            prime_num_capsules: Primary capsules count
            prime_out_ch: Primary capsules out channels
            prime_kernel: Primary capsules kernel size
            prime_stride: Primary capsules stride
            secondary_num_capsules: Secondary capsules count
            secondary_out_channels=Secondary capsules out channels
            maxlen: sentence max length taken into account
    
    """
    def __init__(
        self,
        conv_in_ch,
        conv_out_ch,
        conv_kernel,
        conv_stride,
        prime_num_capsules,
        prime_out_ch,
        prime_kernel,
        prime_stride,
        secondary_num_capsules,
        secondary_out_channels,
        input_len,
    ):
        super(CapsNet, self).__init__()
        self._secondary_out_size=secondary_out_channels * secondary_num_capsules
        
        self.conv_layer = ConvLayer(
            in_channels=conv_in_ch,
            out_channels=conv_out_ch,
            kernel_size=conv_kernel,
            stride=conv_stride,
        )
        conv_layer_output = calculate_conv_output(
            input_=input_len, 
            kernel=conv_kernel, 
            padding=0, 
            stride=conv_stride,
        )
        
        prime_caps_conv_output = calculate_conv_output(
            input_=conv_layer_output, 
            kernel=prime_kernel, 
            padding=0, 
            stride=prime_stride,
        )
        
        self.primary_caps = PrimaryCaps(
            num_capsules=prime_num_capsules, 
            in_channels=conv_out_ch, 
            out_channels=prime_out_ch, 
            kernel_size=prime_kernel, 
            stride=prime_stride,
            conv_out_size=prime_caps_conv_output,
        )
        
        self.secondary_caps = SecondaryCaps(
            num_capsules=secondary_num_capsules,
            num_routes=prime_caps_conv_output * prime_out_ch,
            in_channels=prime_num_capsules,
            out_channels=secondary_out_channels,
        )

        self.fc = nn.Linear(secondary_out_channels * secondary_num_capsules, input_len)
        self.tanh = nn.Tanh()
        self.capsule_softmax = torch.nn.Softmax(dim=1)

    def forward(self, data):
        output = self.secondary_caps(self.primary_caps(self.conv_layer(data)))
        output = output.reshape(-1, self._secondary_out_size)
        output = F.relu(output)
        output = self.fc(output)
        return self.capsule_softmax(self.tanh(output))

from torch.nn.parameter import Parameter
from torch.nn import init
class SelfAttention(torch.nn.Module):
    """Self attention
    
    Args:
        wv_dim: word vector sizeluence
        maxlen: sentence max length taken into account
    
    """
    def __init__(self, wv_dim, maxlen):
        super(SelfAttention, self).__init__()
        self.wv_dim = wv_dim

        # max sentence length -- batch 2nd dim size
        self.maxlen = maxlen
        self.M = Parameter(torch.empty(size=(wv_dim, wv_dim)))
        init.kaiming_uniform_(self.M.data)

        self.tanh = nn.Tanh()
        self.attention_softmax = torch.nn.Softmax(dim=1)

    def forward(self, input_embeddings):
        # (b, wv, 1)
        mean_embedding = torch.mean(input_embeddings, (1,)).unsqueeze(2)

        # (wv, wv) x (b, wv, 1) -> (b, wv, 1)
        product_1 = torch.matmul(self.M, mean_embedding)

        # (b, maxlen, wv) x (b, wv, 1) -> (b, maxlen, 1)
        product_2 = torch.matmul(input_embeddings, product_1).squeeze(2)

        results = self.attention_softmax(self.tanh(product_2))

        return results

    def extra_repr(self):
        return 'wv_dim={}, maxlen={}'.format(self.wv_dim, self.maxlen)
    


# below from .spline import *
import torch


def B_batch(x, grid, k=0, extend=True, device='cpu'):
    '''
    evaludate x on B-spline bases
    
    Args:
    -----
        x : 2D torch.tensor
            inputs, shape (number of splines, number of samples)
        grid : 2D torch.tensor
            grids, shape (number of splines, number of grid points)
        k : int
            the piecewise polynomial order of splines.
        extend : bool
            If True, k points are extended on both ends. If False, no extension (zero boundary condition). Default: True
        device : str
            devicde
    
    Returns:
    --------
        spline values : 3D torch.tensor
            shape (number of splines, number of B-spline bases (coeffcients), number of samples). The numbef of B-spline bases = number of grid points + k - 1.
      
    Example
    -------
    >>> num_spline = 5
    >>> num_sample = 100
    >>> num_grid_interval = 10
    >>> k = 3
    >>> x = torch.normal(0,1,size=(num_spline, num_sample))
    >>> grids = torch.einsum('i,j->ij', torch.ones(num_spline,), torch.linspace(-1,1,steps=num_grid_interval+1))
    >>> B_batch(x, grids, k=k).shape
    torch.Size([5, 13, 100])
    '''

    # x shape: (size, x); grid shape: (size, grid)
    def extend_grid(grid, k_extend=0):
        # pad k to left and right
        # grid shape: (batch, grid)
        h = (grid[:, [-1]] - grid[:, [0]]) / (grid.shape[1] - 1)

        for i in range(k_extend):
            grid = torch.cat([grid[:, [0]] - h, grid], dim=1)
            grid = torch.cat([grid, grid[:, [-1]] + h], dim=1)
        grid = grid.to(device)
        return grid

    if extend == True:
        grid = extend_grid(grid, k_extend=k)

    grid = grid.unsqueeze(dim=2).to(device)
    x = x.unsqueeze(dim=1).to(device)

    if k == 0:
        value = (x >= grid[:, :-1]) * (x < grid[:, 1:])
    else:
        B_km1 = B_batch(x[:, 0], grid=grid[:, :, 0], k=k - 1, extend=False, device=device)
        value = (x - grid[:, :-(k + 1)]) / (grid[:, k:-1] - grid[:, :-(k + 1)]) * B_km1[:, :-1] + (grid[:, k + 1:] - x) / (grid[:, k + 1:] - grid[:, 1:(-k)]) * B_km1[:, 1:]
    return value


def coef2curve(x_eval, grid, coef, k, device="cpu"):
    '''
    converting B-spline coefficients to B-spline curves. Evaluate x on B-spline curves (summing up B_batch results over B-spline basis).
    
    Args:
    -----
        x_eval : 2D torch.tensor)
            shape (number of splines, number of samples)
        grid : 2D torch.tensor)
            shape (number of splines, number of grid points)
        coef : 2D torch.tensor)
            shape (number of splines, number of coef params). number of coef params = number of grid intervals + k
        k : int
            the piecewise polynomial order of splines.
        device : str
            devicde
        
    Returns:
    --------
        y_eval : 2D torch.tensor
            shape (number of splines, number of samples)
        
    Example
    -------
    >>> num_spline = 5
    >>> num_sample = 100
    >>> num_grid_interval = 10
    >>> k = 3
    >>> x_eval = torch.normal(0,1,size=(num_spline, num_sample))
    >>> grids = torch.einsum('i,j->ij', torch.ones(num_spline,), torch.linspace(-1,1,steps=num_grid_interval+1))
    >>> coef = torch.normal(0,1,size=(num_spline, num_grid_interval+k))
    >>> coef2curve(x_eval, grids, coef, k=k).shape
    torch.Size([5, 100])
    '''
    # x_eval: (size, batch), grid: (size, grid), coef: (size, coef)
    # coef: (size, coef), B_batch: (size, coef, batch), summer over coef
    if coef.dtype != x_eval.dtype:
        coef = coef.to(x_eval.dtype)
    y_eval = torch.einsum('ij,ijk->ik', coef, B_batch(x_eval, grid, k, device=device))
    return y_eval


def curve2coef(x_eval, y_eval, grid, k, device="cpu"):
    '''
    converting B-spline curves to B-spline coefficients using least squares.
    
    Args:
    -----
        x_eval : 2D torch.tensor
            shape (number of splines, number of samples)
        y_eval : 2D torch.tensor
            shape (number of splines, number of samples)
        grid : 2D torch.tensor
            shape (number of splines, number of grid points)
        k : int
            the piecewise polynomial order of splines.
        device : str
            devicde
        
    Example
    -------
    >>> num_spline = 5
    >>> num_sample = 100
    >>> num_grid_interval = 10
    >>> k = 3
    >>> x_eval = torch.normal(0,1,size=(num_spline, num_sample))
    >>> y_eval = torch.normal(0,1,size=(num_spline, num_sample))
    >>> grids = torch.einsum('i,j->ij', torch.ones(num_spline,), torch.linspace(-1,1,steps=num_grid_interval+1))
    >>> curve2coef(x_eval, y_eval, grids, k=k).shape
    torch.Size([5, 13])
    '''
    # x_eval: (size, batch); y_eval: (size, batch); grid: (size, grid); k: scalar
    mat = B_batch(x_eval, grid, k, device=device).permute(0, 2, 1).to(y_eval.dtype)
    coef = torch.linalg.lstsq(mat.to('cpu'), y_eval.unsqueeze(dim=2).to('cpu')).solution[:, :, 0]  # sometimes 'cuda' version may diverge
    return coef.to(device)

import torch
import torch.nn as nn
import numpy as np
# from .spline import *


class KANLayer(nn.Module):
    """
    KANLayer class
    

    Attributes:
    -----------
        in_dim: int
            input dimension
        out_dim: int
            output dimension
        size: int
            the number of splines = input dimension * output dimension
        k: int
            the piecewise polynomial order of splines
        grid: 2D torch.float
            grid points
        noises: 2D torch.float
            injected noises to splines at initialization (to break degeneracy)
        coef: 2D torch.tensor
            coefficients of B-spline bases
        scale_base: 1D torch.float
            magnitude of the residual function b(x)
        scale_sp: 1D torch.float
            mangitude of the spline function spline(x)
        base_fun: fun
            residual function b(x)
        mask: 1D torch.float
            mask of spline functions. setting some element of the mask to zero means setting the corresponding activation to zero function.
        grid_eps: float in [0,1]
            a hyperparameter used in update_grid_from_samples. When grid_eps = 0, the grid is uniform; when grid_eps = 1, the grid is partitioned using percentiles of samples. 0 < grid_eps < 1 interpolates between the two extremes.
        weight_sharing: 1D tensor int
            allow spline activations to share parameters
        lock_counter: int
            counter how many activation functions are locked (weight sharing)
        lock_id: 1D torch.int
            the id of activation functions that are locked
        device: str
            device
    
    Methods:
    --------
        __init__():
            initialize a KANLayer
        forward():
            forward 
        update_grid_from_samples():
            update grids based on samples' incoming activations
        initialize_grid_from_parent():
            initialize grids from another model
        get_subset():
            get subset of the KANLayer (used for pruning)
        lock():
            lock several activation functions to share parameters
        unlock():
            unlock already locked activation functions
    """

    def __init__(self, in_dim=3, out_dim=2, num=5, k=3, noise_scale=0.1, scale_base=1.0, scale_sp=1.0, base_fun=torch.nn.SiLU(), grid_eps=0.02, grid_range=[-1, 1], sp_trainable=True, sb_trainable=True, device='cpu'):
        ''''
        initialize a KANLayer
        
        Args:
        -----
            in_dim : int
                input dimension. Default: 2.
            out_dim : int
                output dimension. Default: 3.
            num : int
                the number of grid intervals = G. Default: 5.
            k : int
                the order of piecewise polynomial. Default: 3.
            noise_scale : float
                the scale of noise injected at initialization. Default: 0.1.
            scale_base : float
                the scale of the residual function b(x). Default: 1.0.
            scale_sp : float
                the scale of the base function spline(x). Default: 1.0.
            base_fun : function
                residual function b(x). Default: torch.nn.SiLU()
            grid_eps : float
                When grid_eps = 0, the grid is uniform; when grid_eps = 1, the grid is partitioned using percentiles of samples. 0 < grid_eps < 1 interpolates between the two extremes. Default: 0.02.
            grid_range : list/np.array of shape (2,)
                setting the range of grids. Default: [-1,1].
            sp_trainable : bool
                If true, scale_sp is trainable. Default: True.
            sb_trainable : bool
                If true, scale_base is trainable. Default: True.
            device : str
                device
            
        Returns:
        --------
            self
            
        Example
        -------
        >>> model = KANLayer(in_dim=3, out_dim=5)
        >>> (model.in_dim, model.out_dim)
        (3, 5)
        '''
        super(KANLayer, self).__init__()
        # size 
        self.size = size = out_dim * in_dim
        self.out_dim = out_dim
        self.in_dim = in_dim
        self.num = num
        self.k = k

        # shape: (size, num)
        self.grid = torch.einsum('i,j->ij', torch.ones(size, device=device), torch.linspace(grid_range[0], grid_range[1], steps=num + 1, device=device))
        self.grid = torch.nn.Parameter(self.grid).requires_grad_(False)
        noises = (torch.rand(size, self.grid.shape[1]) - 1 / 2) * noise_scale / num
        noises = noises.to(device)
        # shape: (size, coef)
        self.coef = torch.nn.Parameter(curve2coef(self.grid, noises, self.grid, k, device))
        if isinstance(scale_base, float):
            self.scale_base = torch.nn.Parameter(torch.ones(size, device=device) * scale_base).requires_grad_(sb_trainable)  # make scale trainable
        else:
            self.scale_base = torch.nn.Parameter(torch.FloatTensor(scale_base).to(device)).requires_grad_(sb_trainable)
        self.scale_sp = torch.nn.Parameter(torch.ones(size, device=device) * scale_sp).requires_grad_(sp_trainable)  # make scale trainable
        self.base_fun = base_fun

        self.mask = torch.nn.Parameter(torch.ones(size, device=device)).requires_grad_(False)
        self.grid_eps = grid_eps
        self.weight_sharing = torch.arange(size)
        self.lock_counter = 0
        self.lock_id = torch.zeros(size)
        self.device = "cuda"

    def forward(self, x):
        '''
        KANLayer forward given input x
        
        Args:
        -----
            x : 2D torch.float
                inputs, shape (number of samples, input dimension)
            
        Returns:
        --------
            y : 2D torch.float
                outputs, shape (number of samples, output dimension)
            preacts : 3D torch.float
                fan out x into activations, shape (number of sampels, output dimension, input dimension)
            postacts : 3D torch.float
                the outputs of activation functions with preacts as inputs
            postspline : 3D torch.float
                the outputs of spline functions with preacts as inputs
        
        Example
        -------
        >>> model = KANLayer(in_dim=3, out_dim=5)
        >>> x = torch.normal(0,1,size=(100,3))
        >>> y, preacts, postacts, postspline = model(x)
        >>> y.shape, preacts.shape, postacts.shape, postspline.shape
        (torch.Size([100, 5]),
         torch.Size([100, 5, 3]),
         torch.Size([100, 5, 3]),
         torch.Size([100, 5, 3]))
        '''
        batch = x.shape[0]
        # x: shape (batch, in_dim) => shape (size, batch) (size = out_dim * in_dim)
        x = torch.einsum('ij,k->ikj', x, torch.ones(self.out_dim, device=self.device)).reshape(batch, self.size).permute(1, 0)
        preacts = x.permute(1, 0).clone().reshape(batch, self.out_dim, self.in_dim)
        base = self.base_fun(x).permute(1, 0)  # shape (batch, size)
        y = coef2curve(x_eval=x, grid=self.grid[self.weight_sharing], coef=self.coef[self.weight_sharing], k=self.k, device=self.device)  # shape (size, batch)
        y = y.permute(1, 0)  # shape (batch, size)
        postspline = y.clone().reshape(batch, self.out_dim, self.in_dim)
        y = self.scale_base.unsqueeze(dim=0) * base + self.scale_sp.unsqueeze(dim=0) * y
        y = self.mask[None, :] * y
        postacts = y.clone().reshape(batch, self.out_dim, self.in_dim)
        y = torch.sum(y.reshape(batch, self.out_dim, self.in_dim), dim=2)  # shape (batch, out_dim)
        # y shape: (batch, out_dim); preacts shape: (batch, in_dim, out_dim)
        # postspline shape: (batch, in_dim, out_dim); postacts: (batch, in_dim, out_dim)
        # postspline is for extension; postacts is for visualization
        return y, preacts, postacts, postspline

    def update_grid_from_samples(self, x):
        '''
        update grid from samples
        
        Args:
        -----
            x : 2D torch.float
                inputs, shape (number of samples, input dimension)
            
        Returns:
        --------
            None
        
        Example
        -------
        >>> model = KANLayer(in_dim=1, out_dim=1, num=5, k=3)
        >>> print(model.grid.data)
        >>> x = torch.linspace(-3,3,steps=100)[:,None]
        >>> model.update_grid_from_samples(x)
        >>> print(model.grid.data)
        tensor([[-1.0000, -0.6000, -0.2000,  0.2000,  0.6000,  1.0000]])
        tensor([[-3.0002, -1.7882, -0.5763,  0.6357,  1.8476,  3.0002]])
        '''
        batch = x.shape[0]
        x = torch.einsum('ij,k->ikj', x, torch.ones(self.out_dim, ).to(self.device)).reshape(batch, self.size).permute(1, 0)
        x_pos = torch.sort(x, dim=1)[0]
        y_eval = coef2curve(x_pos, self.grid, self.coef, self.k, device=self.device)
        num_interval = self.grid.shape[1] - 1
        ids = [int(batch / num_interval * i) for i in range(num_interval)] + [-1]
        grid_adaptive = x_pos[:, ids]
        margin = 0.01
        grid_uniform = torch.cat([grid_adaptive[:, [0]] - margin + (grid_adaptive[:, [-1]] - grid_adaptive[:, [0]] + 2 * margin) * a for a in np.linspace(0, 1, num=self.grid.shape[1])], dim=1)
        self.grid.data = self.grid_eps * grid_uniform + (1 - self.grid_eps) * grid_adaptive
        self.coef.data = curve2coef(x_pos, y_eval, self.grid, self.k, device=self.device)


class FuzzyLayer(nn.Module):
    def __init__(self, input_dim, membership_num):
        super(FuzzyLayer, self).__init__()

        # input_dim: feature number of the dataset
        # membership_num: number of membership function, also known as the class number

        self.input_dim = input_dim
        self.membership_num = membership_num

        self.membership_miu = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)
        self.membership_sigma = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)

        nn.init.xavier_uniform_(self.membership_miu)
        nn.init.ones_(self.membership_sigma)

    def forward(self, input_seq):
        batch_size = input_seq.size()[0]
        input_seq_exp = input_seq.unsqueeze(1).expand(batch_size, self.membership_num, self.input_dim)
        membership_miu_exp = self.membership_miu.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)
        membership_sigma_exp = self.membership_sigma.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)

        fuzzy_membership = torch.mean(torch.exp((-1 / 2) * ((input_seq_exp - membership_miu_exp) / membership_sigma_exp) ** 2), dim=-1)
        return fuzzy_membership

class TextCNNBIGRU(nn.Module):
    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):
        super().__init__()

        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)
        self.convs = nn.ModuleList([
                                    nn.Conv1d(in_channels = embedding_dim, 
                                              out_channels = n_filters, 
                                              kernel_size = fs)
                                    for fs in filter_sizes
                                    ])
        
        
        self.dropout = nn.Dropout(dropout)
        self.num_layers = 1
        self.gru_hidden = 64
        self.gru= nn.GRU(embedding_dim,self.gru_hidden,num_layers=self.num_layers,bidirectional=True)
        self.fc = nn.Linear(len(filter_sizes) * n_filters + (self.gru_hidden * 2), output_dim)
    def forward(self, text):
        #text = [batch size, sent len]
        embedded = self.embedding(text)
        #embedded = [batch size, sent len, emb dim]

        gru_output , hn = self.gru(embedded)

        
        gru_output = gru_output.permute(0,2,1)

        # print(embedded.shape,gru_output.shape)  # torch.Size([32, 512, 128]) torch.Size([32, 512, 256])  #torch.Size([32, 60, 60]) torch.Size([32, 128, 60])

        embedded = embedded.permute(0, 2, 1)
        #embedded = [batch size, emb dim, sent len]

        conved = [F.relu(conv(embedded)) for conv in self.convs]
        #conved_n = [batch size, n_filters, sent len - filter_sizes[n] + 1]

        conved.append(gru_output)
        # print(conved[0].shape)

        pooled = [F.max_pool1d(conv, conv.shape[2]).squeeze(2) for conv in conved]
        #pooled_n = [batch size, n_filters]

        cat = self.dropout(torch.cat(pooled, dim = 1))
        #cat = [batch size, n_filters * len(filter_sizes)]



        return self.fc(cat)

class TextCNN(nn.Module):
    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):
        super().__init__()

        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)
        self.convs = nn.ModuleList([
                                    nn.Conv1d(in_channels = embedding_dim, 
                                              out_channels = n_filters, 
                                              kernel_size = fs)
                                    for fs in filter_sizes
                                    ])
        self.fc = nn.Linear(len(filter_sizes) * n_filters, output_dim)
        self.dropout = nn.Dropout(dropout)
    def forward(self, text):
        #text = [batch size, sent len]
        embedded = self.embedding(text)
        #embedded = [batch size, sent len, emb dim]
        embedded = embedded.permute(0, 2, 1)
        #embedded = [batch size, emb dim, sent len]

        conved = [F.relu(conv(embedded)) for conv in self.convs]
        #conved_n = [batch size, n_filters, sent len - filter_sizes[n] + 1]
        
        pooled = [F.max_pool1d(conv, conv.shape[2]).squeeze(2) for conv in conved]
        #pooled_n = [batch size, n_filters]

        cat = self.dropout(torch.cat(pooled, dim = 1))
        #cat = [batch size, n_filters * len(filter_sizes)]
        return self.fc(cat)

class CNNBiLSTM(nn.Module):
    def __init__(self, input_dim, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout):
        super().__init__()

        self.embedding = nn.Linear(input_dim, embedding_dim)
        self.conv = nn.Conv1d(in_channels=embedding_dim, out_channels=32, kernel_size=1)
        self.rnn = nn.LSTM(32, 
                           hidden_dim, 
                           num_layers=n_layers, 
                           bidirectional=bidirectional, 
                           dropout=dropout)
        self.fc = nn.Linear(hidden_dim * 2, output_dim)
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, metadata):
        #metadata = [batch size, metadata dim]

        embedded = self.dropout(self.embedding(metadata))
        #embedded = [batch size, metadata dim, emb dim]

        embedded = torch.reshape(embedded, (metadata.size(0), 128, 1))

        conved = F.relu(self.conv(embedded))
        #conved = [batch size, n_filters, metadata dim - filter_sizes[n] + 1]

        conved = torch.reshape(conved, (metadata.size(0), 32))

        outputs, (hidden, cell) = self.rnn(conved)
        #outputs = [metadata dim - filter_sizes[n] + 1, batch size, hid dim * num directions]
        #hidden = [num layers * num directions, batch size, hid dim]
        #cell = [num layers * num directions, batch size, hid dim]

        #concat the final forward (hidden[-2,:,:]) and backward (hidden[-1,:,:]) hidden layers
        #and apply dropout
        # hidden = self.dropout(torch.cat((hidden[-1,:], hidden[0,:]), dim = -1))
        #hidden = [batch size, hid dim * num directions]

        return self.fc(outputs)

class TextCap(nn.Module):
    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):
        super().__init__()

        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)
        self.caps_net = CapsNet(
            conv_in_ch=128,
            conv_out_ch=32,
            conv_kernel=3,
            conv_stride=1,
            prime_num_capsules=7,
            prime_out_ch=1,
            prime_kernel=3,
            prime_stride=1,
            secondary_num_capsules=7,
            secondary_out_channels=32,
            input_len=32,
        )
    def get_aspects_importances(self, text_embeddings):
        """Get aspect importances
        
        Args:
            text_embedding: embeddings of a sentence as input
        
        Returns: 
            capsule weights, aspects_importances, weighted_text_emb

        """
        # compute capsule scores, looking at text embeddings average
        caps_weights = self.caps_net(text_embeddings.permute(0, 2, 1))

        # multiplying text embeddings by attention scores -- and summing
        # (matmul: we sum every word embedding's coordinate with attention weights)
        weighted_text_emb = torch.matmul(caps_weights.unsqueeze(1),  # (batch, 1, sentence)
                                         text_embeddings  # (batch, sentence, wv_dim)
                                         ).squeeze()

        # encoding with a simple feed-forward layer (wv_dim) -> (aspects_count)
        raw_importances = self.linear_transform(weighted_text_emb)

        # computing 'aspects distribution in a sentence'
        aspects_importances = self.softmax_aspects(raw_importances)

        return caps_weights, aspects_importances, weighted_text_emb

    def forward(self, text):
        #text = [batch size, sent len]
        embedded = self.embedding(text)
        #embedded = [batch size, sent len, emb dim]
        
        _, aspects_importances, weighted_text_emb = self.get_aspects_importances(embedded)
        
        # print(aspects_importances.shape) ([32, 128]
        return aspects_importances

class Attention(nn.Module):
    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):
        super().__init__()

        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)
        self.attention = SelfAttention(embedding_dim, -1)
        self.linear_transform = torch.nn.Linear(embedding_dim, 6)
        self.softmax_aspects = torch.nn.Softmax(dim=1)
        self.aspects_embeddings = Parameter(torch.empty(size=(embedding_dim, 6)))
    def get_aspects_importances(self, text_embeddings):
        """Get aspect importances
        
        Args:
            text_embedding: embeddings of a sentence as input
        
        Returns: 
            attention weights, aspects_importances, weighted_text_emb

        """
        # compute attention scores, looking at text embeddings average
        attention_weights = self.attention(text_embeddings)

        # multiplying text embeddings by attention scores -- and summing
        # (matmul: we sum every word embedding's coordinate with attention weights)
        weighted_text_emb = torch.matmul(attention_weights.unsqueeze(1),  # (batch, 1, sentence)
                                         text_embeddings  # (batch, sentence, wv_dim)
                                         ).squeeze()
        # encoding with a simple feed-forward layer (wv_dim) -> (aspects_count)
        # raw_importances = self.linear_transform(weighted_text_emb)
        raw_importances = self.linear_transform(weighted_text_emb)

        # computing 'aspects distribution in a sentence'
        # print("raw_importances:",raw_importances.shape)
        aspects_importances = self.softmax_aspects(raw_importances)
        # print("aspects_importances:",aspects_importances.shape)

        return attention_weights, aspects_importances, weighted_text_emb

    def forward(self, text_embeddings):
        
        text_embeddings = self.embedding(text_embeddings)

        # encoding: words embeddings -> sentence embedding, aspects importances
        _, aspects_importances, weighted_text_emb = self.get_aspects_importances(text_embeddings)
        return aspects_importances


class LiarModel(nn.Module):
    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional):
        super().__init__()

        self.textcnn = TextCNN(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)
        self.textcnn2 = TextCNN(vocab_size, input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)
        self.cnn_bilstm = CNNBiLSTM(input_dim_metadata, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout)
        self.fuzzy = FuzzyLayer(output_dim, output_dim)
        self.fuse = nn.Linear(output_dim * 5, output_dim)

        self.textcap = TextCap(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)
        self.attention = Attention(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)
        self.bigru_cnn = TextCNNBIGRU(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)
        self.bigru_cnn2 = TextCNNBIGRU(vocab_size, input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)
    def forward(self, text, metadata_text, metadata_number):
        #text = [batch size, sent len]
        #metadata = [batch size, metadata dim]
        text_output = self.bigru_cnn(text)
        # text_output = self.textcnn(text)
        # metadata_output_text = self.textcnn2(metadata_text)
        metadata_output_text = self.bigru_cnn2(metadata_text)
        metadata_output_number = self.cnn_bilstm(metadata_number)
        metadata_output_fuzzy = self.fuzzy(metadata_output_number)
        
        attention_output = self.attention(text)
        # metadata_attention_output = self.attention2(metadata_text)
        fused_output = self.fuse(torch.cat((text_output,attention_output, metadata_output_text, metadata_output_number, metadata_output_fuzzy), dim=1))

        return fused_output


for num_epochs in [3,5,7,9,12,15,17,20]:
    print("------------",num_epochs)
    vocab_size = 30522
    embedding_dim = 128
    n_filters = 128
    filter_sizes = [3,4,5]
    output_dim = 6
    dropout = 0.5
    padding_idx = 0
    input_dim = 6 * metadata_each_dim
    input_dim_metadata = 5
    hidden_dim = 64
    n_layers = 1
    bidirectional = True

    model = LiarModel(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional).to(DEVICE)


    # Define the optimizer and loss function
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
    criterion = nn.BCEWithLogitsLoss()


    # Record the training process
    Train_acc = []
    Train_loss = []
    Train_macro_f1 = []
    Train_micro_f1 = []

    Val_acc = []
    Val_loss = []
    Val_macro_f1 = []
    Val_micro_f1 = []

    def train(num_epochs, model, train_loader, val_loader, optimizer, criterion, model_save):
        epoch_trained = 0
        train_label_all = []
        train_predict_all = []
        val_label_all = []
        val_predict_all = []
        best_valid_loss = float('inf')

        start_time = time.time()
        for epoch in range(num_epochs):
            epoch_trained += 1
            epoch_start_time = time.time()
            # Training
            model.train()
            train_loss = 0.0
            train_accuracy = 0.0
            for statements, label_onehot, label, metadata_text, metadata_number in train_loader:
                statements = statements.to(DEVICE)
                label_onehot = label_onehot.to(DEVICE)
                label = label.to(DEVICE)
                metadata_text = metadata_text.to(DEVICE)
                metadata_number = metadata_number.to(DEVICE)

                optimizer.zero_grad()
                outputs = model(statements, metadata_text, metadata_number)
                loss = criterion(outputs, label_onehot)
                loss.backward()
                optimizer.step()

                train_loss += loss.item()
                _, train_predicted = torch.max(outputs, 1)
                train_accuracy += sum(train_predicted == label)
                train_predict_all += train_predicted.tolist()
                train_label_all += label.tolist()
            train_loss /= len(train_loader)
            train_accuracy /= len(train_loader.dataset)
            train_macro_f1 = f1_score(train_label_all, train_predict_all, average='macro')
            train_micro_f1 = f1_score(train_label_all, train_predict_all, average='micro')

            Train_acc.append(train_accuracy.tolist())
            Train_loss.append(train_loss)
            Train_macro_f1.append(train_macro_f1)
            Train_micro_f1.append(train_micro_f1)

            # Validation
            model.eval()
            val_loss = 0.0
            val_accuracy = 0.0
            with torch.no_grad():
                for statements, label_onehot, label, metadata_text, metadata_number in val_loader:
                    statements = statements.to(DEVICE)
                    label_onehot = label_onehot.to(DEVICE)
                    label = label.to(DEVICE)
                    metadata_text = metadata_text.to(DEVICE)
                    metadata_number = metadata_number.to(DEVICE)

                    val_outputs = model(statements, metadata_text, metadata_number)
                    val_loss += criterion(val_outputs, label_onehot).item()
                    _, val_predicted = torch.max(val_outputs, 1)
                    val_accuracy += sum(val_predicted == label)
                    val_predict_all += val_predicted.tolist()
                    val_label_all += label.tolist()
            val_loss /= len(val_loader)
            val_accuracy /= len(val_loader.dataset)
            val_macro_f1 = f1_score(val_label_all, val_predict_all, average='macro')
            val_micro_f1 = f1_score(val_label_all, val_predict_all, average='micro')

            Val_acc.append(val_accuracy.tolist())
            Val_loss.append(val_loss)
            Val_macro_f1.append(val_macro_f1)
            Val_micro_f1.append(val_micro_f1)

            if val_loss < best_valid_loss:
                best_valid_loss = val_loss
                torch.save(model.state_dict(), model_save)
                print(f'***** Best Result Updated at Epoch {epoch_trained}, Val Loss: {val_loss:.4f} *****')

            # Print the losses and accuracy
            epoch_end_time = time.time()
            epoch_time = epoch_end_time - epoch_start_time

            print(f"Epoch [{epoch+1}/{num_epochs}], Time: {epoch_time:.2f}s, Train Loss: {train_loss:.4f}, Train Acc: {train_accuracy:.4f}, Train F1 Macro: {train_macro_f1:.4f}, Train F1 Micro: {train_micro_f1:.4f}, Val Loss: {val_loss:.4f}, Val Acc: {val_accuracy:.4f}, Val F1 Macro: {val_macro_f1:.4f}, Val F1 Micro: {val_micro_f1:.4f}")

        end_time = time.time()
        training_time = end_time - start_time
        print(f'Total Training Time: {training_time:.2f}s')


    train(num_epochs, model, train_loader, val_loader, optimizer, criterion, model_save)


    # Evaluate the model on new data
    def test(model, test_loader, model_save):
        model.load_state_dict(torch.load(model_save))
        model.eval()

        test_label_all = []
        test_predict_all = []

        test_loss = 0.0
        test_accuracy = 0.0
        with torch.no_grad():
            for statements, label_onehot, label, metadata_text, metadata_number in test_loader:
                statements = statements.to(DEVICE)
                label_onehot = label_onehot.to(DEVICE)
                label = label.to(DEVICE)
                metadata_text = metadata_text.to(DEVICE)
                metadata_number = metadata_number.to(DEVICE)

                test_outputs = model(statements, metadata_text, metadata_number)
                test_loss += criterion(test_outputs, label_onehot).item()
                _, test_predicted = torch.max(test_outputs, 1)
                
                test_accuracy += sum(test_predicted == label)
                test_predict_all += test_predicted.tolist()
                test_label_all += label.tolist()

        test_loss /= len(test_loader)
        test_accuracy /= len(test_loader.dataset)
        test_macro_f1 = f1_score(test_label_all, test_predict_all, average='macro')
        test_micro_f1 = f1_score(test_label_all, test_predict_all, average='micro')

        print(f'Test Loss: {test_loss:.4f}, Test Acc: {test_accuracy:.4f}, Test F1 Macro: {test_macro_f1:.4f}, Test F1 Micro: {test_micro_f1:.4f}')


    test(model, test_loader, model_save)