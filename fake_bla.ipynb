{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PyTorch Version : 2.1.0+cu121\n", "cuda\n"]}, {"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'C:/Users/<USER>/Desktop/HLAN/FAKE/liar_dataset/train.tsv'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 51\u001b[0m\n\u001b[0;32m     48\u001b[0m label_map \u001b[38;5;241m=\u001b[39m {\u001b[38;5;241m0\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpants-fire\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;241m1\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfalse\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;241m2\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbarely-true\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;241m3\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhalf-true\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;241m4\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmostly-true\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;241m5\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtrue\u001b[39m\u001b[38;5;124m'\u001b[39m}\n\u001b[0;32m     49\u001b[0m label_convert \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpants-fire\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m0\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfalse\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m1\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbarely-true\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m2\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhalf-true\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m3\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmostly-true\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m4\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtrue\u001b[39m\u001b[38;5;124m'\u001b[39m:\u001b[38;5;241m5\u001b[39m}\n\u001b[1;32m---> 51\u001b[0m train_data \u001b[38;5;241m=\u001b[39m \u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_csv\u001b[49m\u001b[43m(\u001b[49m\u001b[43mworksapce\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mtrain.tsv\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msep\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;130;43;01m\\t\u001b[39;49;00m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnames\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mcol\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     52\u001b[0m test_data \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_csv(worksapce \u001b[38;5;241m+\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtest.tsv\u001b[39m\u001b[38;5;124m'\u001b[39m, sep \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\t\u001b[39;00m\u001b[38;5;124m'\u001b[39m, names \u001b[38;5;241m=\u001b[39m col)\n\u001b[0;32m     53\u001b[0m val_data \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_csv(worksapce \u001b[38;5;241m+\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mvalid.tsv\u001b[39m\u001b[38;5;124m'\u001b[39m, sep \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\t\u001b[39;00m\u001b[38;5;124m'\u001b[39m, names \u001b[38;5;241m=\u001b[39m col)\n", "File \u001b[1;32me:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\pandas\\io\\parsers\\readers.py:1026\u001b[0m, in \u001b[0;36mread_csv\u001b[1;34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, date_format, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, encoding_errors, dialect, on_bad_lines, delim_whitespace, low_memory, memory_map, float_precision, storage_options, dtype_backend)\u001b[0m\n\u001b[0;32m   1013\u001b[0m kwds_defaults \u001b[38;5;241m=\u001b[39m _refine_defaults_read(\n\u001b[0;32m   1014\u001b[0m     dialect,\n\u001b[0;32m   1015\u001b[0m     delimiter,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1022\u001b[0m     dtype_backend\u001b[38;5;241m=\u001b[39mdtype_backend,\n\u001b[0;32m   1023\u001b[0m )\n\u001b[0;32m   1024\u001b[0m kwds\u001b[38;5;241m.\u001b[39mupdate(kwds_defaults)\n\u001b[1;32m-> 1026\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_read\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32me:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\pandas\\io\\parsers\\readers.py:620\u001b[0m, in \u001b[0;36m_read\u001b[1;34m(filepath_or_buffer, kwds)\u001b[0m\n\u001b[0;32m    617\u001b[0m _validate_names(kwds\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnames\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m))\n\u001b[0;32m    619\u001b[0m \u001b[38;5;66;03m# Create the parser.\u001b[39;00m\n\u001b[1;32m--> 620\u001b[0m parser \u001b[38;5;241m=\u001b[39m TextFileReader(filepath_or_buffer, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwds)\n\u001b[0;32m    622\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m chunksize \u001b[38;5;129;01mor\u001b[39;00m iterator:\n\u001b[0;32m    623\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m parser\n", "File \u001b[1;32me:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\pandas\\io\\parsers\\readers.py:1620\u001b[0m, in \u001b[0;36mTextFileReader.__init__\u001b[1;34m(self, f, engine, **kwds)\u001b[0m\n\u001b[0;32m   1617\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhas_index_names\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m kwds[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhas_index_names\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[0;32m   1619\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles: IOHandles \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m-> 1620\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_engine \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_engine\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mengine\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32me:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\pandas\\io\\parsers\\readers.py:1880\u001b[0m, in \u001b[0;36mTextFileReader._make_engine\u001b[1;34m(self, f, engine)\u001b[0m\n\u001b[0;32m   1878\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m mode:\n\u001b[0;32m   1879\u001b[0m         mode \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m-> 1880\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles \u001b[38;5;241m=\u001b[39m \u001b[43mget_handle\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1881\u001b[0m \u001b[43m    \u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1882\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1883\u001b[0m \u001b[43m    \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mencoding\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1884\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcompression\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcompression\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1885\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmemory_map\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmemory_map\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1886\u001b[0m \u001b[43m    \u001b[49m\u001b[43mis_text\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mis_text\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1887\u001b[0m \u001b[43m    \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mencoding_errors\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstrict\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1888\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstorage_options\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1889\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1890\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m   1891\u001b[0m f \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles\u001b[38;5;241m.\u001b[39mhandle\n", "File \u001b[1;32me:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\pandas\\io\\common.py:873\u001b[0m, in \u001b[0;36mget_handle\u001b[1;34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[0m\n\u001b[0;32m    868\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(handle, \u001b[38;5;28mstr\u001b[39m):\n\u001b[0;32m    869\u001b[0m     \u001b[38;5;66;03m# Check whether the filename is to be opened in binary mode.\u001b[39;00m\n\u001b[0;32m    870\u001b[0m     \u001b[38;5;66;03m# Binary mode does not support 'encoding' and 'newline'.\u001b[39;00m\n\u001b[0;32m    871\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mencoding \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mmode:\n\u001b[0;32m    872\u001b[0m         \u001b[38;5;66;03m# Encoding\u001b[39;00m\n\u001b[1;32m--> 873\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[0;32m    874\u001b[0m \u001b[43m            \u001b[49m\u001b[43mhandle\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    875\u001b[0m \u001b[43m            \u001b[49m\u001b[43mioargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    876\u001b[0m \u001b[43m            \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mioargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    877\u001b[0m \u001b[43m            \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    878\u001b[0m \u001b[43m            \u001b[49m\u001b[43mnewline\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m    879\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    880\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    881\u001b[0m         \u001b[38;5;66;03m# Binary mode\u001b[39;00m\n\u001b[0;32m    882\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mopen\u001b[39m(handle, ioargs\u001b[38;5;241m.\u001b[39mmode)\n", "\u001b[1;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: 'C:/Users/<USER>/Desktop/HLAN/FAKE/liar_dataset/train.tsv'"]}], "source": ["# ===============================\n", "# Title: Fuzzy Deep Hybrid Network (FDHN) for Fake News Detection\n", "# Author: <PERSON>\n", "# Create Date: Apr 21, 2023\n", "# Revision Date: Otc 23, 2023\n", "# Github: https://github.com/chengxuphd/FDHN\n", "# DOI: https://doi.org/10.1145/3628797.3628971\n", "# Description: This is the source code of FDHN model. The dataset used in this code is LIAR (https://www.cs.ucsb.edu/~william/data/liar_dataset.zip).\n", "# Notes: This is code for the TC+TC+CB+FZ experiment.\n", "# ===============================\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import torch.utils.data as data\n", "from sklearn.metrics import f1_score\n", "from torch.utils.data import DataLoader\n", "from transformers import BertTokenizer, BertForSequenceClassification\n", "\n", "# Fixing the randomness of CUDA.\n", "torch.backends.cudnn.deterministic = True\n", "torch.backends.cudnn.benchmark = False\n", "\n", "np.random.seed(42)\n", "torch.manual_seed(42)\n", "\n", "DEVICE = torch.device(\"cuda\") if torch.cuda.is_available() else torch.device(\"cpu\")\n", "print(\"PyTorch Version : {}\".format(torch.__version__))\n", "print(DEVICE)\n", "\n", "\n", "worksapce = 'C:/Users/<USER>/Desktop/HLAN/FAKE/liar_dataset/'\n", "model_save = 'TC+TC+CB+FZ.pt'\n", "model_name = 'TC+TC+CB+FZ'\n", "num_epochs = 10\n", "batch_size = 32\n", "learning_rate = 1e-3\n", "num_classes = 6\n", "padding_idx = 0\n", "metadata_each_dim = 10\n", "\n", "\n", "col = ['id', 'label', 'statement', 'subject', 'speaker', 'job_title', 'state_info', 'party_affiliation', 'barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts', 'context']\n", "\n", "label_map = {0: 'pants-fire', 1: 'false', 2: 'barely-true', 3: 'half-true', 4: 'mostly-true', 5: 'true'}\n", "label_convert = {'pants-fire': 0, 'false': 1, 'barely-true': 2, 'half-true': 3, 'mostly-true': 4, 'true':5}\n", "\n", "train_data = pd.read_csv(worksapce + 'train.tsv', sep = '\\t', names = col)\n", "test_data = pd.read_csv(worksapce + 'test.tsv', sep = '\\t', names = col)\n", "val_data = pd.read_csv(worksapce + 'valid.tsv', sep = '\\t', names = col)\n", "\n", "# Replace NaN values with 'NaN'\n", "train_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = train_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)\n", "train_data.fillna('NaN', inplace=True)\n", "\n", "test_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = test_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)\n", "test_data.fillna('NaN', inplace=True)\n", "\n", "val_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = val_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)\n", "val_data.fillna('NaN', inplace=True)\n", "\n", "\n", "\n", "\n", "def textProcess(input_text, max_length = -1):\n", "    tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')\n", "    if max_length == -1:\n", "        tokens = tokenizer(input_text, truncation=True, padding=True)\n", "    else:\n", "        tokens = tokenizer(input_text, truncation=True, padding='max_length', max_length=max_length)\n", "    return tokens\n", "\n", "\n", "\n", "# Define a custom dataset for loading the data\n", "class LiarDataset(data.Dataset):\n", "    def __init__(self, data_df, statement, label_onehot, label, subject, speaker, job_title, state_info,\n", "                     party_affiliation, barely_true_counts, false_counts, half_true_counts, mostly_true_counts,\n", "                    pants_on_fire_counts, context):\n", "        self.data_df = data_df\n", "        self.statement = statement\n", "        self.label_onehot = label_onehot\n", "        self.label = label\n", "        self.metadata_text = torch.cat((subject.int(), speaker.int(), job_title.int(), state_info.int(), party_affiliation.int(), \n", "                                   context.int()), dim=-1)\n", "        self.metadata_number = torch.cat((torch.tensor(barely_true_counts, dtype=torch.float).unsqueeze(1), torch.tensor(false_counts, dtype=torch.float).unsqueeze(1), \n", "                                   torch.tensor(half_true_counts, dtype=torch.float).unsqueeze(1), torch.tensor(mostly_true_counts, dtype=torch.float).unsqueeze(1), \n", "                                   torch.tensor(pants_on_fire_counts, dtype=torch.float).unsqueeze(1)), dim=-1)\n", "\n", "\n", "        \n", "    def __len__(self):\n", "        return len(self.data_df)\n", "    \n", "    def __getitem__(self, idx):\n", "        statement = self.statement[idx]\n", "        label_onehot = self.label_onehot[idx]\n", "        label = self.label[idx]\n", "        metadata_text = self.metadata_text[idx]\n", "        metadata_number = self.metadata_number[idx]\n", "        return statement, label_onehot, label, metadata_text, metadata_number\n", "\n", "\n", "# Define the data loaders for training and validation\n", "train_text = torch.tensor(textProcess(train_data['statement'].tolist())['input_ids'])\n", "train_label = torch.nn.functional.one_hot(torch.tensor(train_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)\n", "train_subject = torch.tensor(textProcess(train_data['subject'].tolist(), metadata_each_dim)['input_ids'])\n", "train_speaker = torch.tensor(textProcess(train_data['speaker'].tolist(), metadata_each_dim)['input_ids'])\n", "train_job_title = torch.tensor(textProcess(train_data['job_title'].tolist(), metadata_each_dim)['input_ids'])\n", "train_state_info = torch.tensor(textProcess(train_data['state_info'].tolist(), metadata_each_dim)['input_ids'])\n", "train_party_affiliation = torch.tensor(textProcess(train_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])\n", "train_context = torch.tensor(textProcess(train_data['context'].tolist(), metadata_each_dim)['input_ids'])\n", "\n", "train_dataset = LiarDataset(train_data, train_text, train_label, torch.tensor(train_data['label'].replace(label_convert)), \n", "                            train_subject, train_speaker, train_job_title, \n", "                            train_state_info, train_party_affiliation, \n", "                            train_data['barely_true_counts'].tolist(), train_data['false_counts'].tolist(), \n", "                            train_data['half_true_counts'].tolist(), train_data['mostly_true_counts'].tolist(), \n", "                            train_data['pants_on_fire_counts'].tolist(), train_context)\n", "train_loader = data.DataLoader(train_dataset, batch_size=batch_size, shuffle=True)\n", "\n", "val_text = torch.tensor(textProcess(val_data['statement'].tolist())['input_ids'])\n", "val_label = torch.nn.functional.one_hot(torch.tensor(val_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)\n", "val_subject = torch.tensor(textProcess(val_data['subject'].tolist(), metadata_each_dim)['input_ids'])\n", "val_speaker = torch.tensor(textProcess(val_data['speaker'].tolist(), metadata_each_dim)['input_ids'])\n", "val_job_title = torch.tensor(textProcess(val_data['job_title'].tolist(), metadata_each_dim)['input_ids'])\n", "val_state_info = torch.tensor(textProcess(val_data['state_info'].tolist(), metadata_each_dim)['input_ids'])\n", "val_party_affiliation = torch.tensor(textProcess(val_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])\n", "val_context = torch.tensor(textProcess(val_data['context'].tolist(), metadata_each_dim)['input_ids'])\n", "\n", "val_dataset = LiarDataset(val_data, val_text, val_label, torch.tensor(val_data['label'].replace(label_convert)),\n", "                          val_subject, val_speaker, val_job_title, \n", "                          val_state_info, val_party_affiliation, \n", "                          val_data['barely_true_counts'].tolist(), val_data['false_counts'].tolist(), \n", "                          val_data['half_true_counts'].tolist(), val_data['mostly_true_counts'].tolist(), \n", "                          val_data['pants_on_fire_counts'].tolist(), val_context)\n", "val_loader = data.DataLoader(val_dataset, batch_size=batch_size)\n", "\n", "test_text = torch.tensor(textProcess(test_data['statement'].tolist())['input_ids'])\n", "test_label = torch.nn.functional.one_hot(torch.tensor(test_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)\n", "test_subject = torch.tensor(textProcess(test_data['subject'].tolist(), metadata_each_dim)['input_ids'])\n", "test_speaker = torch.tensor(textProcess(test_data['speaker'].tolist(), metadata_each_dim)['input_ids'])\n", "test_job_title = torch.tensor(textProcess(test_data['job_title'].tolist(), metadata_each_dim)['input_ids'])\n", "test_state_info = torch.tensor(textProcess(test_data['state_info'].tolist(), metadata_each_dim)['input_ids'])\n", "test_party_affiliation = torch.tensor(textProcess(test_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])\n", "test_context = torch.tensor(textProcess(test_data['context'].tolist(), metadata_each_dim)['input_ids'])\n", "\n", "test_dataset = LiarDataset(test_data, test_text, test_label, torch.tensor(test_data['label'].replace(label_convert)),\n", "                          test_subject, test_speaker, test_job_title, \n", "                          test_state_info, test_party_affiliation, \n", "                          test_data['barely_true_counts'].tolist(), test_data['false_counts'].tolist(), \n", "                          test_data['half_true_counts'].tolist(), test_data['mostly_true_counts'].tolist(), \n", "                          test_data['pants_on_fire_counts'].tolist(), test_context)\n", "test_loader = data.DataLoader(test_dataset, batch_size=batch_size)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2025-05-13T15:21:43.211080Z", "start_time": "2025-05-13T15:21:43.194544Z"}}, "outputs": [], "source": ["class FuzzyLayer(nn.<PERSON><PERSON><PERSON>):\n", "    def __init__(self, input_dim, membership_num):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        # input_dim: feature number of the dataset\n", "        # membership_num: number of membership function, also known as the class number\n", "\n", "        self.input_dim = input_dim\n", "        self.membership_num = membership_num\n", "\n", "        self.membership_miu = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        self.membership_sigma = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "\n", "        nn.init.xavier_uniform_(self.membership_miu)\n", "        nn.init.ones_(self.membership_sigma)\n", "\n", "    def forward(self, input_seq):\n", "        batch_size = input_seq.size()[0]\n", "        input_seq_exp = input_seq.unsqueeze(1).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_miu_exp = self.membership_miu.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_sigma_exp = self.membership_sigma.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "\n", "        fuzzy_membership = torch.mean(torch.exp((-1 / 2) * ((input_seq_exp - membership_miu_exp) / membership_sigma_exp) ** 2), dim=-1)\n", "        return fuzzy_membership\n", "\n", "\n", "\n", "class TextCNN(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.convs = nn.ModuleList([\n", "                                    nn.Conv1d(in_channels = embedding_dim, \n", "                                              out_channels = n_filters, \n", "                                              kernel_size = fs)\n", "                                    for fs in filter_sizes\n", "                                    ])\n", "        self.fc = nn.Linear(len(filter_sizes) * n_filters, output_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "    \n", "    def forward(self, text):\n", "        #text = [batch size, sent len]\n", "        embedded = self.embedding(text)\n", "        #embedded = [batch size, sent len, emb dim]\n", "\n", "        embedded = embedded.permute(0, 2, 1)\n", "        #embedded = [batch size, emb dim, sent len]\n", "\n", "        conved = [F.relu(conv(embedded)) for conv in self.convs]\n", "        #conved_n = [batch size, n_filters, sent len - filter_sizes[n] + 1]\n", "\n", "        pooled = [F.max_pool1d(conv, conv.shape[2]).squeeze(2) for conv in conved]\n", "        #pooled_n = [batch size, n_filters]\n", "\n", "        cat = self.dropout(torch.cat(pooled, dim = 1))\n", "        #cat = [batch size, n_filters * len(filter_sizes)]\n", "\n", "        return self.fc(cat)\n", "\n", "class CNNBiLSTM(nn.Module):\n", "    def __init__(self, input_dim, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Linear(input_dim, embedding_dim)\n", "        self.conv = nn.Conv1d(in_channels=embedding_dim, out_channels=32, kernel_size=1)\n", "        self.rnn = nn.LSTM(32, \n", "                           hidden_dim, \n", "                           num_layers=n_layers, \n", "                           bidirectional=bidirectional, \n", "                           dropout=dropout)\n", "        self.fc = nn.Linear(hidden_dim * 2, output_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "    \n", "    def forward(self, metadata):\n", "        #metadata = [batch size, metadata dim]\n", "\n", "        embedded = self.dropout(self.embedding(metadata))\n", "        #embedded = [batch size, metadata dim, emb dim]\n", "\n", "        embedded = torch.reshape(embedded, (metadata.size(0), 128, 1))\n", "\n", "        conved = <PERSON>.relu(self.conv(embedded))\n", "        #conved = [batch size, n_filters, metadata dim - filter_sizes[n] + 1]\n", "\n", "        conved = torch.reshape(conved, (metadata.size(0), 32))\n", "\n", "        outputs, (hidden, cell) = self.rnn(conved)\n", "        #outputs = [metadata dim - filter_sizes[n] + 1, batch size, hid dim * num directions]\n", "        #hidden = [num layers * num directions, batch size, hid dim]\n", "        #cell = [num layers * num directions, batch size, hid dim]\n", "\n", "        #concat the final forward (hidden[-2,:,:]) and backward (hidden[-1,:,:]) hidden layers\n", "        #and apply dropout\n", "        # hidden = self.dropout(torch.cat((hidden[-1,:], hidden[0,:]), dim = -1))\n", "        #hidden = [batch size, hid dim * num directions]\n", "\n", "        return self.fc(outputs)\n", "\n", "\n", "class LiarModel(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional):\n", "        super().__init__()\n", "\n", "        self.textcnn = TextCNN(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.textcnn2 = TextCNN(vocab_size, input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.cnn_bilstm = CNNBiLSTM(input_dim_metadata, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout)\n", "        self.fuzzy = FuzzyLayer(output_dim, output_dim)\n", "        self.fuse = nn.Linear(output_dim * 4, output_dim)\n", "    \n", "    def forward(self, text, metadata_text, metadata_number):\n", "        #text = [batch size, sent len]\n", "        #metadata = [batch size, metadata dim]\n", "\n", "        text_output = self.textcnn(text)\n", "        metadata_output_text = self.textcnn2(metadata_text)\n", "        metadata_output_number = self.cnn_bilstm(metadata_number)\n", "        metadata_output_fuzzy = self.fuzzy(metadata_output_number)\n", "\n", "        fused_output = self.fuse(torch.cat((text_output, metadata_output_text, metadata_output_number, metadata_output_fuzzy), dim=1))\n", "\n", "        return fused_output\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2025-05-13T15:22:33.247312Z", "start_time": "2025-05-13T15:22:33.234162Z"}}, "outputs": [], "source": ["num_epochs = 8"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'metadata_each_dim' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[9], line 8\u001b[0m\n\u001b[0;32m      6\u001b[0m dropout \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0.5\u001b[39m\n\u001b[0;32m      7\u001b[0m padding_idx \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m\n\u001b[1;32m----> 8\u001b[0m input_dim \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m6\u001b[39m \u001b[38;5;241m*\u001b[39m \u001b[43mmetadata_each_dim\u001b[49m\n\u001b[0;32m      9\u001b[0m input_dim_metadata \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m5\u001b[39m\n\u001b[0;32m     10\u001b[0m hidden_dim \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m64\u001b[39m\n", "\u001b[1;31mNameError\u001b[0m: name 'metadata_each_dim' is not defined"]}], "source": ["vocab_size = 30522\n", "embedding_dim = 128\n", "n_filters = 128\n", "filter_sizes = [3,4,5]\n", "output_dim = 6\n", "dropout = 0.5\n", "padding_idx = 0\n", "input_dim = 6 * metadata_each_dim\n", "input_dim_metadata = 5\n", "hidden_dim = 64\n", "n_layers = 1\n", "bidirectional = True\n", "\n", "model = LiarModel(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional).to(DEVICE)\n", "\n", "\n", "# Define the optimizer and loss function\n", "optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)\n", "criterion = nn.BCEWithLogitsLoss()\n", "\n", "\n", "# Record the training process\n", "Train_acc = []\n", "Train_loss = []\n", "Train_macro_f1 = []\n", "Train_micro_f1 = []\n", "\n", "Val_acc = []\n", "Val_loss = []\n", "Val_macro_f1 = []\n", "Val_micro_f1 = []\n", "\n", "def train(num_epochs, model, train_loader, val_loader, optimizer, criterion, model_save):\n", "    epoch_trained = 0\n", "    train_label_all = []\n", "    train_predict_all = []\n", "    val_label_all = []\n", "    val_predict_all = []\n", "    best_valid_loss = float('inf')\n", "\n", "    start_time = time.time()\n", "    for epoch in range(num_epochs):\n", "        epoch_trained += 1\n", "        epoch_start_time = time.time()\n", "        # Training\n", "        model.train()\n", "        train_loss = 0.0\n", "        train_accuracy = 0.0\n", "        for statements, label_onehot, label, metadata_text, metadata_number in train_loader:\n", "            statements = statements.to(DEVICE)\n", "            label_onehot = label_onehot.to(DEVICE)\n", "            label = label.to(DEVICE)\n", "            metadata_text = metadata_text.to(DEVICE)\n", "            metadata_number = metadata_number.to(DEVICE)\n", "\n", "            optimizer.zero_grad()\n", "            outputs = model(statements, metadata_text, metadata_number)\n", "            loss = criterion(outputs, label_onehot)\n", "            loss.backward()\n", "            optimizer.step()\n", "\n", "            train_loss += loss.item()\n", "            _, train_predicted = torch.max(outputs, 1)\n", "            train_accuracy += sum(train_predicted == label)\n", "            train_predict_all += train_predicted.tolist()\n", "            train_label_all += label.tolist()\n", "        train_loss /= len(train_loader)\n", "        train_accuracy /= len(train_loader.dataset)\n", "        train_macro_f1 = f1_score(train_label_all, train_predict_all, average='macro')\n", "        train_micro_f1 = f1_score(train_label_all, train_predict_all, average='micro')\n", "\n", "        Train_acc.append(train_accuracy.tolist())\n", "        Train_loss.append(train_loss)\n", "        Train_macro_f1.append(train_macro_f1)\n", "        Train_micro_f1.append(train_micro_f1)\n", "\n", "        # Validation\n", "        model.eval()\n", "        val_loss = 0.0\n", "        val_accuracy = 0.0\n", "        with torch.no_grad():\n", "            for statements, label_onehot, label, metadata_text, metadata_number in val_loader:\n", "                statements = statements.to(DEVICE)\n", "                label_onehot = label_onehot.to(DEVICE)\n", "                label = label.to(DEVICE)\n", "                metadata_text = metadata_text.to(DEVICE)\n", "                metadata_number = metadata_number.to(DEVICE)\n", "\n", "                val_outputs = model(statements, metadata_text, metadata_number)\n", "                val_loss += criterion(val_outputs, label_onehot).item()\n", "                _, val_predicted = torch.max(val_outputs, 1)\n", "                val_accuracy += sum(val_predicted == label)\n", "                val_predict_all += val_predicted.tolist()\n", "                val_label_all += label.tolist()\n", "        val_loss /= len(val_loader)\n", "        val_accuracy /= len(val_loader.dataset)\n", "        val_macro_f1 = f1_score(val_label_all, val_predict_all, average='macro')\n", "        val_micro_f1 = f1_score(val_label_all, val_predict_all, average='micro')\n", "\n", "        Val_acc.append(val_accuracy.tolist())\n", "        Val_loss.append(val_loss)\n", "        Val_macro_f1.append(val_macro_f1)\n", "        Val_micro_f1.append(val_micro_f1)\n", "\n", "        if val_loss < best_valid_loss:\n", "            best_valid_loss = val_loss\n", "            torch.save(model.state_dict(), model_save)\n", "            print(f'***** Best Result Updated at Epoch {epoch_trained}, Val Loss: {val_loss:.4f} *****')\n", "\n", "        # Print the losses and accuracy\n", "        epoch_end_time = time.time()\n", "        epoch_time = epoch_end_time - epoch_start_time\n", "\n", "        print(f\"Epoch [{epoch+1}/{num_epochs}], Time: {epoch_time:.2f}s, Train Loss: {train_loss:.4f}, Train Acc: {train_accuracy:.4f}, Train F1 Macro: {train_macro_f1:.4f}, Train F1 Micro: {train_micro_f1:.4f}, Val Loss: {val_loss:.4f}, Val Acc: {val_accuracy:.4f}, Val F1 Macro: {val_macro_f1:.4f}, Val F1 Micro: {val_micro_f1:.4f}\")\n", "\n", "    end_time = time.time()\n", "    training_time = end_time - start_time\n", "    print(f'Total Training Time: {training_time:.2f}s')\n", "\n", "\n", "train(num_epochs, model, train_loader, val_loader, optimizer, criterion, model_save)\n", "\n", "\n", "# Evaluate the model on new data\n", "def test(model, test_loader, model_save):\n", "    model.load_state_dict(torch.load(model_save))\n", "    model.eval()\n", "\n", "    test_label_all = []\n", "    test_predict_all = []\n", "\n", "    test_loss = 0.0\n", "    test_accuracy = 0.0\n", "    with torch.no_grad():\n", "        for statements, label_onehot, label, metadata_text, metadata_number in test_loader:\n", "            statements = statements.to(DEVICE)\n", "            label_onehot = label_onehot.to(DEVICE)\n", "            label = label.to(DEVICE)\n", "            metadata_text = metadata_text.to(DEVICE)\n", "            metadata_number = metadata_number.to(DEVICE)\n", "\n", "            test_outputs = model(statements, metadata_text, metadata_number)\n", "            test_loss += criterion(test_outputs, label_onehot).item()\n", "            _, test_predicted = torch.max(test_outputs, 1)\n", "            \n", "            test_accuracy += sum(test_predicted == label)\n", "            test_predict_all += test_predicted.tolist()\n", "            test_label_all += label.tolist()\n", "\n", "    test_loss /= len(test_loader)\n", "    test_accuracy /= len(test_loader.dataset)\n", "    test_macro_f1 = f1_score(test_label_all, test_predict_all, average='macro')\n", "    test_micro_f1 = f1_score(test_label_all, test_predict_all, average='micro')\n", "\n", "    print(f'Test Loss: {test_loss:.4f}, Test Acc: {test_accuracy:.4f}, Test F1 Macro: {test_macro_f1:.4f}, Test F1 Micro: {test_micro_f1:.4f}')\n", "\n", "\n", "test(model, test_loader, model_save)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2025-05-13T15:23:06.666871Z", "start_time": "2025-05-13T15:23:06.662939Z"}}, "outputs": [], "source": ["torch.cuda.empty_cache()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class FuzzyLayer(nn.<PERSON><PERSON><PERSON>):\n", "    def __init__(self, input_dim, membership_num):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        # input_dim: feature number of the dataset\n", "        # membership_num: number of membership function, also known as the class number\n", "\n", "        self.input_dim = input_dim\n", "        self.membership_num = membership_num\n", "\n", "        self.membership_miu = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        self.membership_sigma = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "\n", "        nn.init.xavier_uniform_(self.membership_miu)\n", "        nn.init.ones_(self.membership_sigma)\n", "\n", "    def forward(self, input_seq):\n", "        batch_size = input_seq.size()[0]\n", "        input_seq_exp = input_seq.unsqueeze(1).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_miu_exp = self.membership_miu.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_sigma_exp = self.membership_sigma.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "\n", "        fuzzy_membership = torch.mean(torch.exp((-1 / 2) * ((input_seq_exp - membership_miu_exp) / membership_sigma_exp) ** 2), dim=-1)\n", "        # print(fuzzy_membership.shape)  #torch.Size([32, 6])\n", "        return fuzzy_membership\n", "\n", "## senti batch_size = 128\n", "class SentAttNet(nn.Module):\n", "    def __init__(self,  hidden_size=50):\n", "        super(SentAttNet, self).__init__()\n", "        self.sent_weight = nn.Parameter(torch.Tensor(2 * hidden_size, 10))\n", "        self.sent_bias = nn.Parameter(torch.Tensor(10))\n", "        self.context_weight = nn.Parameter(torch.Tensor(10, 1))\n", "        self.gru1 = nn.GRU(510, hidden_size, bidirectional=True)  #将GRU改成LSTM\n", "        self.gru2 = nn.GRU(509, hidden_size, bidirectional=True)\n", "        self.gru3 = nn.GRU(508, hidden_size, bidirectional=True)\n", "        self.gru4 = nn.GRU(58, hidden_size, bidirectional=True)\n", "        self.gru5 = nn.GRU(57, hidden_size, bidirectional=True)\n", "        self.gru6 = nn.GRU(56, hidden_size, bidirectional=True)\n", "        self._create_weights(mean=0.0, std=0.05)\n", "        self.hidden_size = hidden_size\n", "    def _create_weights(self, mean=0.0, std=0.05):\n", "        self.sent_weight.data.normal_(mean, std)\n", "        self.context_weight.data.normal_(mean, std)\n", "\n", "    def forward(self, input, hidden_state):\n", "#         f_output, h_output = self.gru(input, hidden_state)\n", "        input.to('cuda')\n", "        hidden_state.to('cuda')\n", "        input_shape = input.size()\n", "        if(input_shape[-1]==510):\n", "            f_output, h_output = self.gru1(input, hidden_state)\n", "        elif(input_shape[-1]==509):\n", "            f_output, h_output = self.gru2(input, hidden_state)\n", "        elif(input_shape[-1]==508):\n", "            f_output, h_output = self.gru3(input, hidden_state)\n", "#         elif(input_shape[-1]==58):\n", "#             f_output, h_output = self.gru4(input, hidden_state)\n", "#         elif(input_shape[-1]==57):\n", "#             f_output, h_output = self.gru5(input, hidden_state)\n", "#         else:\n", "# #             print(input_shape)\n", "#             f_output, h_output = self.gru6(input, hidden_state)\n", "        output = matrix_mul(f_output, self.sent_weight, self.context_weight, self.sent_bias)\n", "        output = F.softmax(output)\n", "        output = element_wise_mul(output.permute(1,0,2), f_output.permute(1,0,2))\n", "\n", "        return output, h_output\n", "    \n", "def element_wise_mul(input1, input2):\n", "    feature_list = []\n", "    for feature_1, feature_2 in zip(input1, input2):\n", "        feature = (feature_1 * feature_2).sum(dim=0)\n", "        feature_list.append(feature)\n", "\n", "    output = torch.stack(feature_list, dim=0)\n", "    return output\n", "\n", "def matrix_mul(input, weight, context_weight,  bias=False):\n", "    input = torch.matmul(input, weight)\n", "    input = input + bias\n", "    input = torch.tanh(input)\n", "    input = torch.matmul(input, context_weight)\n", "\n", "    return input\n", "\n", "class TextCNN(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.convs = nn.ModuleList([\n", "                                    nn.Conv1d(in_channels = embedding_dim, \n", "                                              out_channels = n_filters, \n", "                                              kernel_size = fs)\n", "                                    for fs in filter_sizes\n", "                                    ])      \n", "        \n", "        self.fc = nn.Linear(len(filter_sizes) * n_filters, output_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "        self.batch_size = 128\n", "        self.sent_hidden_size = 50\n", "        self._init_hidden_state()\n", "        self.sent = SentAttNet( hidden_size=50)\n", "        \n", "    def _init_hidden_state(self, last_batch_size=None):\n", "        if last_batch_size:\n", "            batch_size = last_batch_size\n", "        else:\n", "            batch_size = self.batch_size\n", "        self.sent_hidden_state = torch.zeros(2, batch_size, self.sent_hidden_size)\n", "        if torch.cuda.is_available():\n", "            self.sent_hidden_state = self.sent_hidden_state.cuda()\n", "    \n", "    def forward(self, text):\n", "        #text = [batch size, sent len]\n", "        embedded = self.embedding(text)\n", "        #embedded = [batch size, sent len, emb dim]\n", "\n", "        embedded = embedded.permute(0, 2, 1)\n", "        #embedded = [batch size, emb dim, sent len]\n", "#         print(\"embedded:\",embedded.shape)\n", "        conved = [F.relu(conv(embedded)) for conv in self.convs]\n", "        #conved_n = [batch size, n_filters, sent len - filter_sizes[n] + 1]\n", "#          因为conved是list，GRU因为时间步需要tensor\n", "#         for i in range(len(conved)):\n", "#             print(\"i:\",conved[i].shape)\n", "\n", "        # conv1 = []\n", "        # for conv in conved:\n", "        #     conv , self.sent_hidden_state= self.sent(conv , self.sent_hidden_state)\n", "        #     conv1.append(conv)\n", "        \n", "        pooled = [F.max_pool1d(conv, conv.shape[2]).squeeze(2) for conv in conved]\n", "        #pooled_n = [batch size, n_filters]\n", "        cat = self.dropout(torch.cat(pooled, dim = 1))\n", "        #cat = [batch size, n_filters * len(filter_sizes)]\n", "\n", "        return self.fc(cat)\n", "\n", "class CNNBiLSTM(nn.Module):\n", "    def __init__(self, input_dim, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Linear(input_dim, embedding_dim)\n", "        self.conv = nn.Conv1d(in_channels=embedding_dim, out_channels=32, kernel_size=1)\n", "        self.rnn = nn.LSTM(32, \n", "                           hidden_dim, \n", "                           num_layers=n_layers, \n", "                           bidirectional=bidirectional, \n", "                           dropout=dropout)\n", "        self.fc = nn.Linear(hidden_dim * 2, output_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "    \n", "    def forward(self, metadata):\n", "        #metadata = [batch size, metadata dim]\n", "\n", "        embedded = self.dropout(self.embedding(metadata))\n", "        #embedded = [batch size, metadata dim, emb dim]\n", "\n", "        embedded = torch.reshape(embedded, (metadata.size(0), 128, 1))\n", "\n", "        conved = <PERSON>.relu(self.conv(embedded))\n", "        #conved = [batch size, n_filters, metadata dim - filter_sizes[n] + 1]\n", "        conved = torch.reshape(conved, (metadata.size(0), 32))\n", "        outputs, (hidden, cell) = self.rnn(conved)\n", "        \n", "        #outputs = [metadata dim - filter_sizes[n] + 1, batch size, hid dim * num directions]\n", "        #hidden = [num layers * num directions, batch size, hid dim]\n", "        #cell = [num layers * num directions, batch size, hid dim]\n", "\n", "        #concat the final forward (hidden[-2,:,:]) and backward (hidden[-1,:,:]) hidden layers\n", "        #and apply dropout\n", "        # hidden = self.dropout(torch.cat((hidden[-1,:], hidden[0,:]), dim = -1))\n", "        #hidden = [batch size, hid dim * num directions]\n", "\n", "        return self.fc(outputs)\n", "\n", "\n", "class LiarModel(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional):\n", "        super().__init__()\n", "\n", "        self.textcnn = TextCNN(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.textcnn2 = TextCNN(vocab_size, input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.cnn_bilstm = CNNBiLSTM(input_dim_metadata, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout)\n", "        self.fuzzy = FuzzyLayer(output_dim, output_dim)\n", "        self.fuse = nn.Linear(output_dim * 4, output_dim)\n", "    \n", "    def forward(self, text, metadata_text, metadata_number):\n", "        #text = [batch size, sent len]\n", "        #metadata = [batch size, metadata dim]\n", "\n", "        text_output = self.textcnn(text)\n", "        metadata_output_text = self.textcnn2(metadata_text)\n", "        metadata_output_number = self.cnn_bilstm(metadata_number)\n", "        metadata_output_fuzzy = self.fuzzy(metadata_output_number)\n", "\n", "        fused_output = self.fuse(torch.cat((text_output, metadata_output_text, metadata_output_number, metadata_output_fuzzy), dim=1))\n", "\n", "        return fused_output\n", "\n", "\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}}, "nbformat": 4, "nbformat_minor": 2}