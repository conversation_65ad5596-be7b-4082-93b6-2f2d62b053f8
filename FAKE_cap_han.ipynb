{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "A module that was compiled using NumPy 1.x cannot be run in\n", "NumPy 2.0.2 as it may crash. To support both 1.x and 2.x\n", "versions of NumPy, modules must be compiled with NumPy 2.0.\n", "Some module may need to rebuild instead e.g. with 'pybind11>=2.12'.\n", "\n", "If you are a user of the module, the easiest solution will be to\n", "downgrade to 'numpy<2' or try to upgrade the affected module.\n", "We expect that some modules will need time to support NumPy 2.\n", "\n", "Traceback (most recent call last):  File \"D:\\Program Files (x86)\\Python\\lib\\runpy.py\", line 197, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "  File \"D:\\Program Files (x86)\\Python\\lib\\runpy.py\", line 87, in _run_code\n", "    exec(code, run_globals)\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\traitlets\\config\\application.py\", line 1075, in launch_instance\n", "    app.start()\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\ipykernel\\kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\tornado\\platform\\asyncio.py\", line 205, in start\n", "    self.asyncio_loop.run_forever()\n", "  File \"D:\\Program Files (x86)\\Python\\lib\\asyncio\\base_events.py\", line 596, in run_forever\n", "    self._run_once()\n", "  File \"D:\\Program Files (x86)\\Python\\lib\\asyncio\\base_events.py\", line 1890, in _run_once\n", "    handle._run()\n", "  File \"D:\\Program Files (x86)\\Python\\lib\\asyncio\\events.py\", line 80, in _run\n", "    self._context.run(self._callback, *self._args)\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\ipykernel\\kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\ipykernel\\kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\ipykernel\\kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\ipykernel\\ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\ipykernel\\kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\ipykernel\\ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\ipykernel\\zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\IPython\\core\\interactiveshell.py\", line 3048, in run_cell\n", "    result = self._run_cell(\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\IPython\\core\\interactiveshell.py\", line 3103, in _run_cell\n", "    result = runner(coro)\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\IPython\\core\\async_helpers.py\", line 129, in _pseudo_sync_runner\n", "    coro.send(None)\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\IPython\\core\\interactiveshell.py\", line 3308, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\IPython\\core\\interactiveshell.py\", line 3490, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\IPython\\core\\interactiveshell.py\", line 3550, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20568\\2659147198.py\", line 15, in <module>\n", "    import torch\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\torch\\__init__.py\", line 1382, in <module>\n", "    from .functional import *  # noqa: F403\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\torch\\functional.py\", line 7, in <module>\n", "    import torch.nn.functional as F\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\torch\\nn\\__init__.py\", line 1, in <module>\n", "    from .modules import *  # noqa: F403\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\torch\\nn\\modules\\__init__.py\", line 35, in <module>\n", "    from .transformer import TransformerEncoder, TransformerDecoder, \\\n", "  File \"E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\torch\\nn\\modules\\transformer.py\", line 20, in <module>\n", "    device: torch.device = torch.device(torch._C._get_default_device()),  # torch.device('cpu'),\n", "E:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\torch\\nn\\modules\\transformer.py:20: UserWarning: Failed to initialize NumPy: _ARRAY_API not found (Triggered internally at C:\\actions-runner\\_work\\pytorch\\pytorch\\builder\\windows\\pytorch\\torch\\csrc\\utils\\tensor_numpy.cpp:84.)\n", "  device: torch.device = torch.device(torch._C._get_default_device()),  # torch.device('cpu'),\n"]}, {"name": "stdout", "output_type": "stream", "text": ["PyTorch Version : 2.1.0+cu121\n", "cuda\n"]}, {"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'C:/Users/<USER>/Desktop/HLAN/FAKE/liar_dataset/train.tsv'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 51\u001b[0m\n\u001b[0;32m     48\u001b[0m label_map \u001b[38;5;241m=\u001b[39m {\u001b[38;5;241m0\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpants-fire\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;241m1\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfalse\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;241m2\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbarely-true\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;241m3\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhalf-true\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;241m4\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmostly-true\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;241m5\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtrue\u001b[39m\u001b[38;5;124m'\u001b[39m}\n\u001b[0;32m     49\u001b[0m label_convert \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpants-fire\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m0\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfalse\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m1\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbarely-true\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m2\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhalf-true\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m3\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmostly-true\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m4\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtrue\u001b[39m\u001b[38;5;124m'\u001b[39m:\u001b[38;5;241m5\u001b[39m}\n\u001b[1;32m---> 51\u001b[0m train_data \u001b[38;5;241m=\u001b[39m \u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_csv\u001b[49m\u001b[43m(\u001b[49m\u001b[43mworksapce\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mtrain.tsv\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msep\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;130;43;01m\\t\u001b[39;49;00m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnames\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mcol\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     52\u001b[0m test_data \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_csv(worksapce \u001b[38;5;241m+\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtest.tsv\u001b[39m\u001b[38;5;124m'\u001b[39m, sep \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\t\u001b[39;00m\u001b[38;5;124m'\u001b[39m, names \u001b[38;5;241m=\u001b[39m col)\n\u001b[0;32m     53\u001b[0m val_data \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_csv(worksapce \u001b[38;5;241m+\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mvalid.tsv\u001b[39m\u001b[38;5;124m'\u001b[39m, sep \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\t\u001b[39;00m\u001b[38;5;124m'\u001b[39m, names \u001b[38;5;241m=\u001b[39m col)\n", "File \u001b[1;32mE:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\pandas\\io\\parsers\\readers.py:1026\u001b[0m, in \u001b[0;36mread_csv\u001b[1;34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, date_format, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, encoding_errors, dialect, on_bad_lines, delim_whitespace, low_memory, memory_map, float_precision, storage_options, dtype_backend)\u001b[0m\n\u001b[0;32m   1013\u001b[0m kwds_defaults \u001b[38;5;241m=\u001b[39m _refine_defaults_read(\n\u001b[0;32m   1014\u001b[0m     dialect,\n\u001b[0;32m   1015\u001b[0m     delimiter,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1022\u001b[0m     dtype_backend\u001b[38;5;241m=\u001b[39mdtype_backend,\n\u001b[0;32m   1023\u001b[0m )\n\u001b[0;32m   1024\u001b[0m kwds\u001b[38;5;241m.\u001b[39mupdate(kwds_defaults)\n\u001b[1;32m-> 1026\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_read\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mE:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\pandas\\io\\parsers\\readers.py:620\u001b[0m, in \u001b[0;36m_read\u001b[1;34m(filepath_or_buffer, kwds)\u001b[0m\n\u001b[0;32m    617\u001b[0m _validate_names(kwds\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnames\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m))\n\u001b[0;32m    619\u001b[0m \u001b[38;5;66;03m# Create the parser.\u001b[39;00m\n\u001b[1;32m--> 620\u001b[0m parser \u001b[38;5;241m=\u001b[39m TextFileReader(filepath_or_buffer, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwds)\n\u001b[0;32m    622\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m chunksize \u001b[38;5;129;01mor\u001b[39;00m iterator:\n\u001b[0;32m    623\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m parser\n", "File \u001b[1;32mE:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\pandas\\io\\parsers\\readers.py:1620\u001b[0m, in \u001b[0;36mTextFileReader.__init__\u001b[1;34m(self, f, engine, **kwds)\u001b[0m\n\u001b[0;32m   1617\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhas_index_names\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m kwds[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhas_index_names\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[0;32m   1619\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles: IOHandles \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m-> 1620\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_engine \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_engine\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mengine\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mE:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\pandas\\io\\parsers\\readers.py:1880\u001b[0m, in \u001b[0;36mTextFileReader._make_engine\u001b[1;34m(self, f, engine)\u001b[0m\n\u001b[0;32m   1878\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m mode:\n\u001b[0;32m   1879\u001b[0m         mode \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m-> 1880\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles \u001b[38;5;241m=\u001b[39m \u001b[43mget_handle\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1881\u001b[0m \u001b[43m    \u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1882\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1883\u001b[0m \u001b[43m    \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mencoding\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1884\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcompression\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcompression\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1885\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmemory_map\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmemory_map\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1886\u001b[0m \u001b[43m    \u001b[49m\u001b[43mis_text\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mis_text\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1887\u001b[0m \u001b[43m    \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mencoding_errors\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstrict\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1888\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstorage_options\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1889\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1890\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m   1891\u001b[0m f \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles\u001b[38;5;241m.\u001b[39mhandle\n", "File \u001b[1;32mE:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\pandas\\io\\common.py:873\u001b[0m, in \u001b[0;36mget_handle\u001b[1;34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[0m\n\u001b[0;32m    868\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(handle, \u001b[38;5;28mstr\u001b[39m):\n\u001b[0;32m    869\u001b[0m     \u001b[38;5;66;03m# Check whether the filename is to be opened in binary mode.\u001b[39;00m\n\u001b[0;32m    870\u001b[0m     \u001b[38;5;66;03m# Binary mode does not support 'encoding' and 'newline'.\u001b[39;00m\n\u001b[0;32m    871\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mencoding \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mmode:\n\u001b[0;32m    872\u001b[0m         \u001b[38;5;66;03m# Encoding\u001b[39;00m\n\u001b[1;32m--> 873\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[0;32m    874\u001b[0m \u001b[43m            \u001b[49m\u001b[43mhandle\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    875\u001b[0m \u001b[43m            \u001b[49m\u001b[43mioargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    876\u001b[0m \u001b[43m            \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mioargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    877\u001b[0m \u001b[43m            \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    878\u001b[0m \u001b[43m            \u001b[49m\u001b[43mnewline\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m    879\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    880\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    881\u001b[0m         \u001b[38;5;66;03m# Binary mode\u001b[39;00m\n\u001b[0;32m    882\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mopen\u001b[39m(handle, ioargs\u001b[38;5;241m.\u001b[39mmode)\n", "\u001b[1;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: 'C:/Users/<USER>/Desktop/HLAN/FAKE/liar_dataset/train.tsv'"]}], "source": ["# ===============================\n", "# Title: Fuzzy Deep Hybrid Network (FDHN) for Fake News Detection\n", "# Author: <PERSON>\n", "# Create Date: Apr 21, 2023\n", "# Revision Date: Otc 23, 2023\n", "# Github: https://github.com/chengxuphd/FDHN\n", "# DOI: https://doi.org/10.1145/3628797.3628971\n", "# Description: This is the source code of FDHN model. The dataset used in this code is LIAR (https://www.cs.ucsb.edu/~william/data/liar_dataset.zip).\n", "# Notes: This is code for the TC+TC+CB+FZ experiment.\n", "# ===============================\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import torch.utils.data as data\n", "from sklearn.metrics import f1_score\n", "from torch.utils.data import DataLoader\n", "from transformers import BertTokenizer, BertForSequenceClassification\n", "\n", "# Fixing the randomness of CUDA.\n", "torch.backends.cudnn.deterministic = True\n", "torch.backends.cudnn.benchmark = False\n", "\n", "np.random.seed(42)\n", "torch.manual_seed(42)\n", "\n", "DEVICE = torch.device(\"cuda\") if torch.cuda.is_available() else torch.device(\"cpu\")\n", "print(\"PyTorch Version : {}\".format(torch.__version__))\n", "print(DEVICE)\n", "\n", "\n", "worksapce = 'C:/Users/<USER>/Desktop/HLAN/FAKE/liar_dataset/'\n", "model_save = 'TC+TC+CB+FZ.pt'\n", "model_name = 'TC+TC+CB+FZ'\n", "num_epochs = 10\n", "batch_size = 32\n", "learning_rate = 1e-3\n", "num_classes = 6\n", "padding_idx = 0\n", "metadata_each_dim = 10\n", "\n", "\n", "col = ['id', 'label', 'statement', 'subject', 'speaker', 'job_title', 'state_info', 'party_affiliation', 'barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts', 'context']\n", "\n", "label_map = {0: 'pants-fire', 1: 'false', 2: 'barely-true', 3: 'half-true', 4: 'mostly-true', 5: 'true'}\n", "label_convert = {'pants-fire': 0, 'false': 1, 'barely-true': 2, 'half-true': 3, 'mostly-true': 4, 'true':5}\n", "\n", "train_data = pd.read_csv(worksapce + 'train.tsv', sep = '\\t', names = col)\n", "test_data = pd.read_csv(worksapce + 'test.tsv', sep = '\\t', names = col)\n", "val_data = pd.read_csv(worksapce + 'valid.tsv', sep = '\\t', names = col)\n", "\n", "# Replace NaN values with 'NaN'\n", "train_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = train_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)\n", "train_data.fillna('NaN', inplace=True)\n", "\n", "test_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = test_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)\n", "test_data.fillna('NaN', inplace=True)\n", "\n", "val_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = val_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)\n", "val_data.fillna('NaN', inplace=True)\n", "\n", "\n", "\n", "\n", "def textProcess(input_text, max_length = -1):\n", "    tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')\n", "    if max_length == -1:\n", "        tokens = tokenizer(input_text, truncation=True, padding=True)\n", "    else:\n", "        tokens = tokenizer(input_text, truncation=True, padding='max_length', max_length=max_length)\n", "    return tokens\n", "\n", "\n", "\n", "# Define a custom dataset for loading the data\n", "class LiarDataset(data.Dataset):\n", "    def __init__(self, data_df, statement, label_onehot, label, subject, speaker, job_title, state_info,\n", "                     party_affiliation, barely_true_counts, false_counts, half_true_counts, mostly_true_counts,\n", "                    pants_on_fire_counts, context):\n", "        self.data_df = data_df\n", "        self.statement = statement\n", "        self.label_onehot = label_onehot\n", "        self.label = label\n", "        self.metadata_text = torch.cat((subject.int(), speaker.int(), job_title.int(), state_info.int(), party_affiliation.int(), \n", "                                   context.int()), dim=-1)\n", "        self.metadata_number = torch.cat((torch.tensor(barely_true_counts, dtype=torch.float).unsqueeze(1), torch.tensor(false_counts, dtype=torch.float).unsqueeze(1), \n", "                                   torch.tensor(half_true_counts, dtype=torch.float).unsqueeze(1), torch.tensor(mostly_true_counts, dtype=torch.float).unsqueeze(1), \n", "                                   torch.tensor(pants_on_fire_counts, dtype=torch.float).unsqueeze(1)), dim=-1)\n", "\n", "\n", "        \n", "    def __len__(self):\n", "        return len(self.data_df)\n", "    \n", "    def __getitem__(self, idx):\n", "        statement = self.statement[idx]\n", "        label_onehot = self.label_onehot[idx]\n", "        label = self.label[idx]\n", "        metadata_text = self.metadata_text[idx]\n", "        metadata_number = self.metadata_number[idx]\n", "        return statement, label_onehot, label, metadata_text, metadata_number\n", "\n", "\n", "# Define the data loaders for training and validation\n", "train_text = torch.tensor(textProcess(train_data['statement'].tolist())['input_ids'])\n", "train_label = torch.nn.functional.one_hot(torch.tensor(train_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)\n", "train_subject = torch.tensor(textProcess(train_data['subject'].tolist(), metadata_each_dim)['input_ids'])\n", "train_speaker = torch.tensor(textProcess(train_data['speaker'].tolist(), metadata_each_dim)['input_ids'])\n", "train_job_title = torch.tensor(textProcess(train_data['job_title'].tolist(), metadata_each_dim)['input_ids'])\n", "train_state_info = torch.tensor(textProcess(train_data['state_info'].tolist(), metadata_each_dim)['input_ids'])\n", "train_party_affiliation = torch.tensor(textProcess(train_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])\n", "train_context = torch.tensor(textProcess(train_data['context'].tolist(), metadata_each_dim)['input_ids'])\n", "\n", "train_dataset = LiarDataset(train_data, train_text, train_label, torch.tensor(train_data['label'].replace(label_convert)), \n", "                            train_subject, train_speaker, train_job_title, \n", "                            train_state_info, train_party_affiliation, \n", "                            train_data['barely_true_counts'].tolist(), train_data['false_counts'].tolist(), \n", "                            train_data['half_true_counts'].tolist(), train_data['mostly_true_counts'].tolist(), \n", "                            train_data['pants_on_fire_counts'].tolist(), train_context)\n", "train_loader = data.DataLoader(train_dataset, batch_size=batch_size, shuffle=True)\n", "\n", "val_text = torch.tensor(textProcess(val_data['statement'].tolist())['input_ids'])\n", "val_label = torch.nn.functional.one_hot(torch.tensor(val_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)\n", "val_subject = torch.tensor(textProcess(val_data['subject'].tolist(), metadata_each_dim)['input_ids'])\n", "val_speaker = torch.tensor(textProcess(val_data['speaker'].tolist(), metadata_each_dim)['input_ids'])\n", "val_job_title = torch.tensor(textProcess(val_data['job_title'].tolist(), metadata_each_dim)['input_ids'])\n", "val_state_info = torch.tensor(textProcess(val_data['state_info'].tolist(), metadata_each_dim)['input_ids'])\n", "val_party_affiliation = torch.tensor(textProcess(val_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])\n", "val_context = torch.tensor(textProcess(val_data['context'].tolist(), metadata_each_dim)['input_ids'])\n", "\n", "val_dataset = LiarDataset(val_data, val_text, val_label, torch.tensor(val_data['label'].replace(label_convert)),\n", "                          val_subject, val_speaker, val_job_title, \n", "                          val_state_info, val_party_affiliation, \n", "                          val_data['barely_true_counts'].tolist(), val_data['false_counts'].tolist(), \n", "                          val_data['half_true_counts'].tolist(), val_data['mostly_true_counts'].tolist(), \n", "                          val_data['pants_on_fire_counts'].tolist(), val_context)\n", "val_loader = data.DataLoader(val_dataset, batch_size=batch_size)\n", "\n", "test_text = torch.tensor(textProcess(test_data['statement'].tolist())['input_ids'])\n", "test_label = torch.nn.functional.one_hot(torch.tensor(test_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)\n", "test_subject = torch.tensor(textProcess(test_data['subject'].tolist(), metadata_each_dim)['input_ids'])\n", "test_speaker = torch.tensor(textProcess(test_data['speaker'].tolist(), metadata_each_dim)['input_ids'])\n", "test_job_title = torch.tensor(textProcess(test_data['job_title'].tolist(), metadata_each_dim)['input_ids'])\n", "test_state_info = torch.tensor(textProcess(test_data['state_info'].tolist(), metadata_each_dim)['input_ids'])\n", "test_party_affiliation = torch.tensor(textProcess(test_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])\n", "test_context = torch.tensor(textProcess(test_data['context'].tolist(), metadata_each_dim)['input_ids'])\n", "\n", "test_dataset = LiarDataset(test_data, test_text, test_label, torch.tensor(test_data['label'].replace(label_convert)),\n", "                          test_subject, test_speaker, test_job_title, \n", "                          test_state_info, test_party_affiliation, \n", "                          test_data['barely_true_counts'].tolist(), test_data['false_counts'].tolist(), \n", "                          test_data['half_true_counts'].tolist(), test_data['mostly_true_counts'].tolist(), \n", "                          test_data['pants_on_fire_counts'].tolist(), test_context)\n", "test_loader = data.DataLoader(test_dataset, batch_size=batch_size)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.Size([10240, 512])"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["train_text.shape"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torch.autograd import Variable\n", "\n", "\n", "def squash(inputs, axis=-1):\n", "    norm = torch.norm(inputs, p=2, dim=axis, keepdim=True)\n", "    scale = norm**2 / (1 + norm**2) / (norm + 1e-8)\n", "    return scale * inputs\n", "\n", "\n", "class DenseCapsule(nn.Module):\n", "    def __init__(self, in_num_caps, in_dim_caps, out_num_caps, out_dim_caps, routings=3):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.in_num_caps = in_num_caps\n", "        self.in_dim_caps = in_dim_caps\n", "        self.out_num_caps = out_num_caps\n", "        self.out_dim_caps = out_dim_caps\n", "        self.routings = routings\n", "        self.weight = nn.Parameter(0.01 * torch.randn(out_num_caps, in_num_caps, out_dim_caps, in_dim_caps))\n", "\n", "    def forward(self, x):\n", "        # x.size=[batch, in_num_caps, in_dim_caps]\n", "        # expanded to    [batch, 1,            in_num_caps, in_dim_caps,  1]\n", "        # weight.size   =[       out_num_caps, in_num_caps, out_dim_caps, in_dim_caps]\n", "        # torch.matmul: [out_dim_caps, in_dim_caps] x [in_dim_caps, 1] -> [out_dim_caps, 1]\n", "        # => x_hat.size =[batch, out_num_caps, in_num_caps, out_dim_caps]\n", "        x_hat = torch.squeeze(torch.matmul(self.weight, x[:, None, :, :, None]), dim=-1)\n", "\n", "        # In forward pass, `x_hat_detached` = `x_hat`;\n", "        # In backward, no gradient can flow from `x_hat_detached` back to `x_hat`.\n", "        x_hat_detached = x_hat.detach()\n", "\n", "        # The prior for coupling coefficient, initialized as zeros.\n", "        # b.size = [batch, out_num_caps, in_num_caps]\n", "        b = Variable(torch.zeros(x.size(0), self.out_num_caps, self.in_num_caps)).cuda()\n", "\n", "        assert self.routings > 0, 'The \\'routings\\' should be > 0.'\n", "        for i in range(self.routings):\n", "            # c.size = [batch, out_num_caps, in_num_caps]\n", "            c = F.softmax(b, dim=1)\n", "\n", "            # At last iteration, use `x_hat` to compute `outputs` in order to backpropagate gradient\n", "            if i == self.routings - 1:\n", "                # c.size expanded to [batch, out_num_caps, in_num_caps, 1           ]\n", "                # x_hat.size     =   [batch, out_num_caps, in_num_caps, out_dim_caps]\n", "                # => outputs.size=   [batch, out_num_caps, 1,           out_dim_caps]\n", "                outputs = squash(torch.sum(c[:, :, :, None] * x_hat, dim=-2, keepdim=True))\n", "                # outputs = squash(torch.matmul(c[:, :, None, :], x_hat))  # alternative way\n", "            else:  # Otherwise, use `x_hat_detached` to update `b`. No gradients flow on this path.\n", "                outputs = squash(torch.sum(c[:, :, :, None] * x_hat_detached, dim=-2, keepdim=True))\n", "                # outputs = squash(torch.matmul(c[:, :, None, :], x_hat_detached))  # alternative way\n", "\n", "                # outputs.size       =[batch, out_num_caps, 1,           out_dim_caps]\n", "                # x_hat_detached.size=[batch, out_num_caps, in_num_caps, out_dim_caps]\n", "                # => b.size          =[batch, out_num_caps, in_num_caps]\n", "                b = b + torch.sum(outputs * x_hat_detached, dim=-1)\n", "\n", "        return torch.squeeze(outputs, dim=-2)\n", "\n", "\n", "class PrimaryCapsule(nn.Module):\n", "    def __init__(self, in_channels, out_channels, dim_caps, kernel_size, stride=1, padding=0):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.dim_caps = dim_caps\n", "        self.conv2d = nn.Conv2d(in_channels, out_channels, kernel_size=kernel_size, stride=stride, padding=padding)\n", "\n", "    def forward(self, x):\n", "        outputs = self.conv2d(x)\n", "        # print(outputs.shape)\n", "        outputs = outputs.view(x.size(0), -1, self.dim_caps)\n", "        return squash(outputs)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["class CapsuleNet2(nn.Module):\n", "    def __init__(self, input_size, classes, routings):\n", "        super(CapsuleNet2, self).__init__()\n", "        self.input_size = input_size\n", "        self.classes = classes\n", "        self.routings = routings\n", "\n", "        # Layer 1: Just a conventional Conv2D layer\n", "        self.conv1 = nn.Conv2d(3, 1, kernel_size=2, stride=1, padding=0)\n", "        \n", "        # Layer 2: Conv2D layer with `squash` activation, then reshape to [None, num_caps, dim_caps]\n", "        self.primarycaps = PrimaryCapsule(1, 1, 18, kernel_size=5, stride=2, padding=0)\n", "\n", "        # Layer 3: Capsule layer. Routing algorithm works here.\n", "        self.digitcaps = DenseCapsule(in_num_caps=93, in_dim_caps=18,\n", "                                      out_num_caps=classes, out_dim_caps=100, routings=routings)\n", "\n", "\n", "        self.relu = nn.ReLU()\n", "\n", "    def forward(self, x, y=None):\n", "        x = self.relu(self.conv1(x))\n", "        # print(\"x1.shape: \",x.shape) #[128, 64, 48, 98])  64和第一层有关 #[32, 1, 57, 127])\n", "        x = self.primarycaps(x) #torch.Size([128, 60, 8])\n", "        # print(\"x2.shape: \",x.shape) #\n", "        x = self.digitcaps(x) #[128, 16, 5])\n", "        # print(\"x3.shape: \",x.shape)\n", "        return x\n", "\n", "def caps_loss(y_true, y_pred, x, x_recon, lam_recon):\n", "    L = y_true * torch.clamp(0.9 - y_pred, min=0.) ** 2 + \\\n", "        0.5 * (1 - y_true) * torch.clamp(y_pred - 0.1, min=0.) ** 2\n", "    L_margin = L.sum(dim=1).mean()\n", "\n", "    L_recon = nn.MSELoss()(x_recon, x)\n", "\n", "    return L_margin + lam_recon * L_recon\n", "\n", "class capnet2(nn.<PERSON><PERSON><PERSON>):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.convs = nn.ModuleList([\n", "                                    nn.Conv1d(in_channels = embedding_dim, \n", "                                              out_channels = n_filters, \n", "                                              kernel_size = fs)\n", "                                    for fs in [3,3,3]\n", "                                    ])   \n", "        \n", "        self.cap = CapsuleNet2(([ 3, 58, 128]), classes=6, routings=3)\n", "        \n", "    def forward(self, text):\n", "        #text = [batch size, sent len]\n", "        embedded = self.embedding(text)\n", "        #embedded = [batch size, sent len, emb dim]\n", "\n", "        embedded = embedded.permute(0, 2, 1)\n", "        #embedded = [batch size, emb dim, sent len]                         # torch.<PERSON><PERSON>([32, 128, 58])    # torch.<PERSON><PERSON>([32, 128, 57])    # torch.Size([32, 128, 56])\n", "#         \n", "\n", "        conved = [F.relu(conv(embedded)) for conv in self.convs]\n", "        tacked_tensor = torch.stack([conved[0], conved[1], conved[2]], dim=3)\n", "        tacked_tensor = tacked_tensor.permute(0, 3, 2, 1) # [batch size, dim, sent len, embed_dim]   ([32, 3, 58, 128])\n", "        tacked_tensor = self.cap(tacked_tensor)           #[32, 6, 100]\n", "        tacked_tensor = F.max_pool1d(tacked_tensor, tacked_tensor.shape[2]).squeeze()\n", "        # print(tacked_tensor.shape)\n", "        return tacked_tensor\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["class CapsuleNet1(nn.Module):\n", "    def __init__(self, input_size, classes, routings):\n", "        super(CapsuleNet1, self).__init__()\n", "        self.input_size = input_size\n", "        self.classes = classes\n", "        self.routings = routings\n", "\n", "        # Layer 1: Just a conventional Conv2D layer\n", "        self.conv1 = nn.Conv2d(3, 1, kernel_size=2, stride=1, padding=0)\n", "        \n", "        # Layer 2: Conv2D layer with `squash` activation, then reshape to [None, num_caps, dim_caps]\n", "        self.primarycaps = PrimaryCapsule(1, 1, 62, kernel_size=5, stride=2, padding=0)\n", "\n", "        # Layer 3: Capsule layer. Routing algorithm works here.\n", "        self.digitcaps = DenseCapsule(in_num_caps=253, in_dim_caps=62,\n", "                                      out_num_caps=classes, out_dim_caps=100, routings=routings)\n", "\n", "\n", "        self.relu = nn.ReLU()\n", "\n", "    def forward(self, x, y=None):\n", "        x = self.relu(self.conv1(x))\n", "        # print(\"x1.shape: \",x.shape) #[128, 64, 48, 98])  64和第一层有关 #[32, 1, 57, 127])\n", "        x = self.primarycaps(x) #torch.Size([128, 60, 8])\n", "        # print(\"x2.shape: \",x.shape) #\n", "        x = self.digitcaps(x) #[128, 16, 5])\n", "        # print(\"x3.shape: \",x.shape)\n", "        return x\n", "\n", "def caps_loss(y_true, y_pred, x, x_recon, lam_recon):\n", "    L = y_true * torch.clamp(0.9 - y_pred, min=0.) ** 2 + \\\n", "        0.5 * (1 - y_true) * torch.clamp(y_pred - 0.1, min=0.) ** 2\n", "    L_margin = L.sum(dim=1).mean()\n", "\n", "    L_recon = nn.MSELoss()(x_recon, x)\n", "\n", "    return L_margin + lam_recon * L_recon\n", "\n", "class capnet1(nn.<PERSON><PERSON><PERSON>):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.convs = nn.ModuleList([\n", "                                    nn.Conv1d(in_channels = embedding_dim, \n", "                                              out_channels = n_filters, \n", "                                              kernel_size = fs)\n", "                                    for fs in [3,3,3]\n", "                                    ])   \n", "        \n", "        self.cap = CapsuleNet1(([ 3, 58, 128]), classes=6, routings=2)\n", "        \n", "    def forward(self, text):\n", "        #text = [batch size, sent len]\n", "        embedded = self.embedding(text)\n", "        #embedded = [batch size, sent len, emb dim]\n", "\n", "        embedded = embedded.permute(0, 2, 1)\n", "        #embedded = [batch size, emb dim, sent len]                         # torch.<PERSON><PERSON>([32, 128, 58])    # torch.<PERSON><PERSON>([32, 128, 57])    # torch.Size([32, 128, 56])\n", "#         \n", "\n", "        conved = [F.relu(conv(embedded)) for conv in self.convs]\n", "        tacked_tensor = torch.stack([conved[0], conved[1], conved[2]], dim=3)\n", "        tacked_tensor = tacked_tensor.permute(0, 3, 2, 1) # [batch size, dim, sent len, embed_dim]   ([32, 3, 58, 128])\n", "        tacked_tensor = self.cap(tacked_tensor)           #[32, 6, 100]\n", "        tacked_tensor = F.max_pool1d(tacked_tensor, tacked_tensor.shape[2]).squeeze()\n", "        # print(tacked_tensor.shape)\n", "        return tacked_tensor\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Epoch：2 在cnn之后加入self.att 、在textual使用胶囊模型能快速达到原文同样的收敛效果 ATT_DIM = 128"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["class FuzzyLayer1(nn.<PERSON><PERSON><PERSON>):  #广义钟形隶属函数\n", "    def __init__(self, input_dim, membership_num):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>1, self).__init__()\n", "\n", "        # input_dim: feature number of the dataset\n", "        # membership_num: number of membership function, also known as the class number\n", "\n", "        self.input_dim = input_dim\n", "        self.membership_num = membership_num\n", "\n", "        self.membership_miu = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        self.membership_a = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        self.membership_b = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "\n", "        nn.init.xavier_uniform_(self.membership_miu)\n", "        nn.init.ones_(self.membership_a)\n", "        nn.init.ones_(self.membership_b)\n", "\n", "    def forward(self, input_seq):\n", "        batch_size = input_seq.size()[0]\n", "        input_seq_exp = input_seq.unsqueeze(1).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_miu_exp = self.membership_miu.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_a_exp = self.membership_a.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_b_exp = self.membership_b.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "\n", "        # fuzzy_membership = torch.mean(torch.exp((-1 / 2) * ((input_seq_exp - membership_miu_exp) / membership_sigma_exp) ** 2), dim=-1)\n", "        # print(fuzzy_membership.shape)  #torch.Size([32, 6])\n", "        fuzzy_membership = torch.mean(1/1+(torch.abs((input_seq_exp - membership_miu_exp)/membership_a_exp)) ** membership_b_exp, dim=-1)\n", "        return fuzzy_membership\n", "\n", "class FuzzyLayer(nn.<PERSON><PERSON><PERSON>):   #高斯\n", "\n", "    def __init__(self, input_dim, membership_num):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        \n", "#         input_dim: feature number of the dataset\n", "#         membership_num: number of membership function, also known as the class number\n", "        \n", "        self.input_dim = input_dim\n", "        self.membership_num = membership_num\n", "        \n", "        self.membership_miu = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        self.membership_sigma = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        \n", "        nn.init.xavier_uniform_(self.membership_miu)\n", "        nn.init.ones_(self.membership_sigma)\n", "    def forward(self, input_seq):\n", "        batch_size = input_seq.size()[0]\n", "        input_seq_exp = input_seq.unsqueeze(1).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_miu_exp = self.membership_miu.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_sigma_exp = self.membership_sigma.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "\n", "        fuzzy_membership = torch.mean(torch.exp((-1 / 2) * ((input_seq_exp - membership_miu_exp) / membership_sigma_exp) ** 2), dim=-1)\n", "        return fuzzy_membership\n", "\n", "\n", "class SelfAttention(nn.Module):\n", "    def __init__(self, input_dim):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.input_dim = input_dim\n", "        \n", "        ### A Simple Linear Neural Network to get the Query Tensor\n", "        self.query = nn.Linear(input_dim, input_dim)\n", "        \n", "        #### A Simple Linear Neural Network to get the Key Tensor\n", "        self.key = nn.Linear(input_dim, input_dim)\n", "        \n", "        ### Similarly for 'V' or Value Tensor\n", "        self.value = nn.Linear(input_dim, input_dim)\n", "        \n", "        ### doing the softmax actovation on dimension = 2, which is the \n", "        ### number of batches of input samples\n", "        self.softmax = nn.Softmax(dim=2)\n", "\n", "    def forward(self, x):\n", "        queries = self.query(x)\n", "        keys = self.key(x)\n", "        values = self.value(x)\n", "        \n", "        ### First step is the product of Query 'Q' and <PERSON> 'K',aftern then diving the\n", "        ### obtained product by 1/sqrt(input_dim)\n", "        scores = torch.bmm(queries, keys.transpose(1, 2)) / (self.input_dim ** 0.5)\n", "        \n", "        ###getting the attention weights\n", "        attention = self.softmax(scores)\n", "        \n", "        ###getting the finaly weighted matrix by mutltiplying\n", "        #### the softmax product output with 'Value' vector\n", "        weighted = torch.bmm(attention, values)\n", "        return weighted\n", "\n", "class TextCNN(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.convs = nn.ModuleList([\n", "                                    nn.Conv1d(in_channels = embedding_dim, \n", "                                              out_channels = n_filters, \n", "                                              kernel_size = fs)\n", "                                    for fs in filter_sizes\n", "                                    ])      \n", "        \n", "        self.fc = nn.Linear(len(filter_sizes) * n_filters, output_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "\n", "        self.attention_dot_self = SelfAttention(input_dim=128)\n", "\n", "    \n", "    def forward(self, text):\n", "        #text = [batch size, sent len]\n", "        # print(\"text:\",text.shape)\n", "        embedded = self.embedding(text)\n", "        #embedded = [batch size, sent len, emb dim]\n", "\n", "        embedded = embedded.permute(0, 2, 1)\n", "        #embedded = [batch size, emb dim, sent len]\n", "        \n", "        conved = [F.relu(conv(embedded)) for conv in self.convs]\n", "        #conved_n = [batch size, n_filters, sent len - filter_sizes[n] + 1]\n", "        # print(\"conved:\",conved[0].shape)  #[32, 128 , 510]\n", "        conved_att = [self.attention_dot_self(conv.permute(0,2,1)).permute(0,2,1) for conv in conved]\n", "        pooled = [F.max_pool1d(conv, conv.shape[2]).squeeze(2) for conv in conved_att]\n", "        #pooled_n = [batch size, n_filters]\n", "        cat = self.dropout(torch.cat(pooled, dim = 1))\n", "        #cat = [batch size, n_filters * len(filter_sizes)]\n", "\n", "        return self.fc(cat)\n", "\n", "def split(a, n): #split in to n part\n", "    k, m = divmod(len(a), n)\n", "    return (a[i*k+min(i, m):(i+1)*k+min(i+1, m)] for i in range(n))\n", "import math\n", "from fightingcv_attention.attention.ExternalAttention import *\n", "\n", "class TextSW(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "        self.hidden_dim = 128\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.rnn = nn.LSTM(embedding_dim, self.hidden_dim, num_layers=2, bidirectional=True, dropout=0.5)\n", "        self.fc = nn.<PERSON>(36, 6)\n", "        self.ea = ExternalAttention(d_model=256,S=8)\n", "        self.pool = nn.AdaptiveAvgPool2d(6)\n", "        self.dropout = nn.Dropout(0.3)\n", "        self.flat = nn.<PERSON><PERSON>()\n", "    def attention_net(self, x, query, mask=None):      #软性注意力机制（key=value=x）\n", "\n", "        d_k = query.size(-1)    \n", "        scores = torch.matmul(query, x.transpose(1, 2)) / math.sqrt(d_k)  #打分机制  scores:[batch, seq_len, seq_len]\n", "        p_attn = F.softmax(scores, dim=-1)  #对最后一个维度归一化得分\n", "        context = torch.matmul(p_attn, x).sum(1)       #对权重化的x求和，[batch, seq_len, hidden_dim*2]->[batch, hidden_dim*2]\n", "        return context, p_attn\n", "    \n", "    def forward(self, text):\n", "        #text = [batch size, sent len]\n", "        embedded = self.embedding(text)     \n", "        # print(embedded.shape) #[32, 512, 128]\n", "        #embedded = [batch size, sent len, emb dim]\n", "        # embedded = embedded.permute(1, 0, 2)\n", "        # embedded = self.dropout(embedded)\n", "        output, (final_hidden_state, final_cell_state) = self.rnn(embedded)               #[batch, seq_len, hidden_dim*2]\n", "        attn_output=self.ea(output)\n", "        attn_output=self.pool(attn_output)\n", "        attn_output=self.flat(attn_output)\n", "        # query = self.dropout(output)  #([32, 512, 1024])\n", "        # attn_output, attention = self.attention_net(output, query)       #和LSTM的不同就在于这一句\n", "        \n", "        logit = self.fc(attn_output)\n", "        \n", "        return logit\n", "       \n", "\n", "class CNNBiLSTM(nn.Module):\n", "    def __init__(self, input_dim, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Linear(input_dim, embedding_dim)\n", "        self.conv = nn.Conv1d(in_channels=embedding_dim, out_channels=32, kernel_size=1)\n", "        self.rnn = nn.LSTM(32, \n", "                           hidden_dim, \n", "                           num_layers=n_layers, \n", "                           bidirectional=bidirectional, \n", "                           dropout=dropout)\n", "        self.fc = nn.Linear(hidden_dim * 2, output_dim)\n", "        \n", "        self.dropout = nn.Dropout(dropout)\n", "    def forward(self, metadata):\n", "        #metadata = [batch size, metadata dim]\n", "\n", "        embedded = self.dropout(self.embedding(metadata))\n", "        #embedded = [batch size, metadata dim, emb dim]\n", "        embedded = torch.reshape(embedded, (metadata.size(0), 128, 1))\n", "\n", "        conved = <PERSON>.relu(self.conv(embedded))\n", "        #conved = [batch size, n_filters, metadata dim - filter_sizes[n] + 1]\n", "        conved = torch.reshape(conved, (metadata.size(0), 32))\n", "        \n", "        outputs, (hidden, cell) = self.rnn(conved)\n", "        \n", "        #outputs = [metadata dim - filter_sizes[n] + 1, batch size, hid dim * num directions]\n", "        #hidden = [num layers * num directions, batch size, hid dim]\n", "        #cell = [num layers * num directions, batch size, hid dim]\n", "\n", "        #concat the final forward (hidden[-2,:,:]) and backward (hidden[-1,:,:]) hidden layers\n", "        #and apply dropout\n", "        # hidden = self.dropout(torch.cat((hidden[-1,:], hidden[0,:]), dim = -1))\n", "        #hidden = [batch size, hid dim * num directions]\n", "\n", "        return self.fc(outputs)\n", "\n", "class LiarModel(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional):\n", "        super().__init__()\n", "\n", "        self.textcnn = TextCNN(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.textcnn2 = TextCNN(vocab_size, input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.cnn_bilstm = CNNBiLSTM(input_dim_metadata, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout)\n", "        self.fuzzy = FuzzyLayer(output_dim, output_dim)\n", "        self.fuse = nn.Linear(output_dim * 4, output_dim)\n", "        # self.capNet1 = capnet1(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.capNet2 = capnet2(vocab_size, input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.fuzzy1 = FuzzyLayer1(output_dim, output_dim)\n", "        # self.TextSW = TextSW(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "    def forward(self, text, metadata_text, metadata_number):\n", "        #text = [batch size, sent len]\n", "        #metadata = [batch size, metadata dim]\n", "\n", "        text_output = self.textcnn(text)\n", "        # text_output = self.TextSW(text)\n", "        # metadata_output_text = self.textcnn2(metadata_text)  #metadata_data[32,60]  metadata_output_text = torch.Size([32, 6])\n", "        # text_output = self.capNet1(text)\n", "        metadata_output_text = self.capNet2(metadata_text)\n", "        metadata_output_number = self.cnn_bilstm(metadata_number)\n", "        metadata_output_fuzzy = self.fuzzy(metadata_output_number)\n", "\n", "        fused_output = self.fuse(torch.cat((text_output, metadata_output_text, metadata_output_number, metadata_output_fuzzy), dim=1))\n", "\n", "        return fused_output\n", "\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### news和textual使用self_ATT"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["class FuzzyLayer1(nn.<PERSON><PERSON><PERSON>):  #广义钟形隶属函数\n", "    def __init__(self, input_dim, membership_num):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>1, self).__init__()\n", "\n", "        # input_dim: feature number of the dataset\n", "        # membership_num: number of membership function, also known as the class number\n", "\n", "        self.input_dim = input_dim\n", "        self.membership_num = membership_num\n", "\n", "        self.membership_miu = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        self.membership_a = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        self.membership_b = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "\n", "        nn.init.xavier_uniform_(self.membership_miu)\n", "        nn.init.ones_(self.membership_a)\n", "        nn.init.ones_(self.membership_b)\n", "\n", "    def forward(self, input_seq):\n", "        batch_size = input_seq.size()[0]\n", "        input_seq_exp = input_seq.unsqueeze(1).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_miu_exp = self.membership_miu.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_a_exp = self.membership_a.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_b_exp = self.membership_b.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "\n", "        # fuzzy_membership = torch.mean(torch.exp((-1 / 2) * ((input_seq_exp - membership_miu_exp) / membership_sigma_exp) ** 2), dim=-1)\n", "        # print(fuzzy_membership.shape)  #torch.Size([32, 6])\n", "        fuzzy_membership = torch.mean(1/1+(torch.abs((input_seq_exp - membership_miu_exp)/membership_a_exp)) ** membership_b_exp, dim=-1)\n", "        return fuzzy_membership\n", "\n", "class FuzzyLayer(nn.<PERSON><PERSON><PERSON>):   #高斯\n", "\n", "    def __init__(self, input_dim, membership_num):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        \n", "#         input_dim: feature number of the dataset\n", "#         membership_num: number of membership function, also known as the class number\n", "        \n", "        self.input_dim = input_dim\n", "        self.membership_num = membership_num\n", "        \n", "        self.membership_miu = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        self.membership_sigma = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        \n", "        nn.init.xavier_uniform_(self.membership_miu)\n", "        nn.init.ones_(self.membership_sigma)\n", "    def forward(self, input_seq):\n", "        batch_size = input_seq.size()[0]\n", "        input_seq_exp = input_seq.unsqueeze(1).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_miu_exp = self.membership_miu.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_sigma_exp = self.membership_sigma.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "\n", "        fuzzy_membership = torch.mean(torch.exp((-1 / 2) * ((input_seq_exp - membership_miu_exp) / membership_sigma_exp) ** 2), dim=-1)\n", "        return fuzzy_membership\n", "\n", "\n", "class SelfAttention(nn.Module):\n", "    def __init__(self, input_dim):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.input_dim = input_dim\n", "        \n", "        ### A Simple Linear Neural Network to get the Query Tensor\n", "        self.query = nn.Linear(input_dim, input_dim)\n", "        \n", "        #### A Simple Linear Neural Network to get the Key Tensor\n", "        self.key = nn.Linear(input_dim, input_dim)\n", "        \n", "        ### Similarly for 'V' or Value Tensor\n", "        self.value = nn.Linear(input_dim, input_dim)\n", "        \n", "        ### doing the softmax actovation on dimension = 2, which is the \n", "        ### number of batches of input samples\n", "        self.softmax = nn.Softmax(dim=2)\n", "\n", "    def forward(self, x):\n", "        queries = self.query(x)\n", "        keys = self.key(x)\n", "        values = self.value(x)\n", "        \n", "        ### First step is the product of Query 'Q' and <PERSON> 'K',aftern then diving the\n", "        ### obtained product by 1/sqrt(input_dim)\n", "        scores = torch.bmm(queries, keys.transpose(1, 2)) / (self.input_dim ** 0.5)\n", "        \n", "        ###getting the attention weights\n", "        attention = self.softmax(scores)\n", "        \n", "        ###getting the finaly weighted matrix by mutltiplying\n", "        #### the softmax product output with 'Value' vector\n", "        weighted = torch.bmm(attention, values)\n", "        return weighted\n", "\n", "class TextCNN(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.convs = nn.ModuleList([\n", "                                    nn.Conv1d(in_channels = embedding_dim, \n", "                                              out_channels = n_filters, \n", "                                              kernel_size = fs)\n", "                                    for fs in filter_sizes\n", "                                    ])      \n", "        \n", "        self.fc = nn.Linear(len(filter_sizes) * n_filters, output_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "\n", "        self.attention_dot_self = SelfAttention(input_dim=128)\n", "\n", "    \n", "    def forward(self, text):\n", "        #text = [batch size, sent len]\n", "        # print(\"text:\",text.shape)\n", "        embedded = self.embedding(text)\n", "        #embedded = [batch size, sent len, emb dim]\n", "\n", "        embedded = embedded.permute(0, 2, 1)\n", "        #embedded = [batch size, emb dim, sent len]\n", "        \n", "        conved = [F.relu(conv(embedded)) for conv in self.convs]\n", "        #conved_n = [batch size, n_filters, sent len - filter_sizes[n] + 1]\n", "        # print(\"conved:\",conved[0].shape)  #[32, 128 , 510]\n", "        conved_att = [self.attention_dot_self(conv.permute(0,2,1)).permute(0,2,1) for conv in conved]\n", "        pooled = [F.max_pool1d(conv, conv.shape[2]).squeeze(2) for conv in conved_att]\n", "        #pooled_n = [batch size, n_filters]\n", "        cat = self.dropout(torch.cat(pooled, dim = 1))\n", "        #cat = [batch size, n_filters * len(filter_sizes)]\n", "\n", "        return self.fc(cat)\n", "\n", "\n", "    \n", "def split(a, n): #split in to n part\n", "    k, m = divmod(len(a), n)\n", "    return (a[i*k+min(i, m):(i+1)*k+min(i+1, m)] for i in range(n))\n", "import math\n", "from fightingcv_attention.attention.ExternalAttention import *\n", "\n", "class TextSW(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "        self.hidden_dim = 128\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.rnn = nn.LSTM(embedding_dim, self.hidden_dim, num_layers=2, bidirectional=True, dropout=0.5)\n", "        self.fc = nn.<PERSON>(36, 6)\n", "        self.ea = ExternalAttention(d_model=256,S=8)\n", "        self.pool = nn.AdaptiveAvgPool2d(6)\n", "        self.dropout = nn.Dropout(0.3)\n", "        self.flat = nn.<PERSON><PERSON>()\n", "    def attention_net(self, x, query, mask=None):      #软性注意力机制（key=value=x）\n", "\n", "        d_k = query.size(-1)    \n", "        scores = torch.matmul(query, x.transpose(1, 2)) / math.sqrt(d_k)  #打分机制  scores:[batch, seq_len, seq_len]\n", "        p_attn = F.softmax(scores, dim=-1)  #对最后一个维度归一化得分\n", "        context = torch.matmul(p_attn, x).sum(1)       #对权重化的x求和，[batch, seq_len, hidden_dim*2]->[batch, hidden_dim*2]\n", "        return context, p_attn\n", "    \n", "    def forward(self, text):\n", "        #text = [batch size, sent len]\n", "        embedded = self.embedding(text)     \n", "        # print(embedded.shape) #[32, 512, 128]\n", "        #embedded = [batch size, sent len, emb dim]\n", "        # embedded = embedded.permute(1, 0, 2)\n", "        # embedded = self.dropout(embedded)\n", "        output, (final_hidden_state, final_cell_state) = self.rnn(embedded)               #[batch, seq_len, hidden_dim*2]\n", "        attn_output=self.ea(output)\n", "        attn_output=self.pool(attn_output)\n", "        attn_output=self.flat(attn_output)\n", "        # query = self.dropout(output)  #([32, 512, 1024])\n", "        # attn_output, attention = self.attention_net(output, query)       #和LSTM的不同就在于这一句\n", "        \n", "        logit = self.fc(attn_output)\n", "        \n", "        return logit\n", "       \n", "\n", "class CNNBiLSTM(nn.Module):\n", "    def __init__(self, input_dim, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Linear(input_dim, embedding_dim)\n", "        self.conv = nn.Conv1d(in_channels=embedding_dim, out_channels=32, kernel_size=1)\n", "        self.rnn = nn.LSTM(32, \n", "                           hidden_dim, \n", "                           num_layers=n_layers, \n", "                           bidirectional=bidirectional, \n", "                           dropout=dropout)\n", "        self.fc = nn.Linear(hidden_dim * 2, output_dim)\n", "        \n", "        self.dropout = nn.Dropout(dropout)\n", "    def forward(self, metadata):\n", "        #metadata = [batch size, metadata dim]\n", "\n", "        embedded = self.dropout(self.embedding(metadata))\n", "        #embedded = [batch size, metadata dim, emb dim]\n", "        embedded = torch.reshape(embedded, (metadata.size(0), 128, 1))\n", "\n", "        conved = <PERSON>.relu(self.conv(embedded))\n", "        #conved = [batch size, n_filters, metadata dim - filter_sizes[n] + 1]\n", "        conved = torch.reshape(conved, (metadata.size(0), 32))\n", "        \n", "        outputs, (hidden, cell) = self.rnn(conved)\n", "        \n", "        #outputs = [metadata dim - filter_sizes[n] + 1, batch size, hid dim * num directions]\n", "        #hidden = [num layers * num directions, batch size, hid dim]\n", "        #cell = [num layers * num directions, batch size, hid dim]\n", "\n", "        #concat the final forward (hidden[-2,:,:]) and backward (hidden[-1,:,:]) hidden layers\n", "        #and apply dropout\n", "        # hidden = self.dropout(torch.cat((hidden[-1,:], hidden[0,:]), dim = -1))\n", "        #hidden = [batch size, hid dim * num directions]\n", "\n", "        return self.fc(outputs)\n", "\n", "class LiarModel(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional):\n", "        super().__init__()\n", "\n", "        self.textcnn = TextCNN(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.textcnn2 = TextCNN(vocab_size, input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.cnn_bilstm = CNNBiLSTM(input_dim_metadata, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout)\n", "        self.fuzzy = FuzzyLayer(output_dim, output_dim)\n", "        self.fuse = nn.Linear(output_dim * 4, output_dim)\n", "        # self.capNet1 = capnet1(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.capNet2 = capnet2(vocab_size, input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.fuzzy1 = FuzzyLayer1(output_dim, output_dim)\n", "        # self.TextSW = TextSW(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "    def forward(self, text, metadata_text, metadata_number):\n", "        #text = [batch size, sent len]\n", "        #metadata = [batch size, metadata dim]\n", "\n", "        text_output = self.textcnn(text)\n", "        # text_output = self.TextSW(text)\n", "        metadata_output_text = self.textcnn2(metadata_text)  #metadata_data[32,60]  metadata_output_text = torch.Size([32, 6])\n", "        # text_output = self.capNet1(text)\n", "        # metadata_output_text = self.capNet2(metadata_text)\n", "        metadata_output_number = self.cnn_bilstm(metadata_number)\n", "        metadata_output_fuzzy = self.fuzzy(metadata_output_number)\n", "\n", "        fused_output = self.fuse(torch.cat((text_output, metadata_output_text, metadata_output_number, metadata_output_fuzzy), dim=1))\n", "\n", "        return fused_output\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--------------- 2\n", "***** Best Result Updated at Epoch 1, Val Loss: 0.3922 *****\n", "Epoch [1/2], Time: 8.35s, Train Loss: 0.4312, Train Acc: 0.2927, Train F1 Macro: 0.2593, Train F1 Micro: 0.2927, Val Loss: 0.3922, Val Acc: 0.3995, Val F1 Macro: 0.3469, Val F1 Micro: 0.3995\n", "***** Best Result Updated at Epoch 2, Val Loss: 0.3723 *****\n", "Epoch [2/2], Time: 8.24s, Train Loss: 0.3726, Train Acc: 0.4434, Train F1 Macro: 0.3479, Train F1 Micro: 0.3680, Val Loss: 0.3723, Val Acc: 0.4229, Val F1 Macro: 0.3707, Val F1 Micro: 0.4112\n", "Total Training Time: 16.58s\n", "Test Loss: 0.3620, Test Acc: 0.4317, Test F1 Macro: 0.4178, Test F1 Micro: 0.4317\n", "--------------- 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\TEXTGCN\\lib\\site-packages\\torch\\nn\\modules\\rnn.py:82: UserWarning: dropout option adds dropout after all but last recurrent layer, so non-zero dropout expects num_layers greater than 1, but got dropout=0.5 and num_layers=1\n", "  warnings.warn(\"dropout option adds dropout after all but last \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["***** Best Result Updated at Epoch 1, Val Loss: 0.3930 *****\n", "Epoch [1/3], Time: 8.16s, Train Loss: 0.4293, Train Acc: 0.3040, Train F1 Macro: 0.2844, Train F1 Micro: 0.3040, Val Loss: 0.3930, Val Acc: 0.4283, Val F1 Macro: 0.3938, Val F1 Micro: 0.4283\n", "***** Best Result Updated at Epoch 2, Val Loss: 0.3555 *****\n", "Epoch [2/3], Time: 7.88s, Train Loss: 0.3739, Train Acc: 0.4383, Train F1 Macro: 0.3568, Train F1 Micro: 0.3711, Val Loss: 0.3555, Val Acc: 0.4642, Val F1 Macro: 0.4249, Val F1 Micro: 0.4463\n", "***** Best Result Updated at Epoch 3, Val Loss: 0.3499 *****\n", "Epoch [3/3], Time: 7.86s, Train Loss: 0.3519, Train Acc: 0.4583, Train F1 Macro: 0.3876, Train F1 Micro: 0.4002, Val Loss: 0.3499, Val Acc: 0.4696, Val F1 Macro: 0.4365, Val F1 Micro: 0.4540\n", "Total Training Time: 23.90s\n", "Test Loss: 0.3512, Test Acc: 0.4388, Test F1 Macro: 0.4313, Test F1 Micro: 0.4388\n", "--------------- 5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\TEXTGCN\\lib\\site-packages\\torch\\nn\\modules\\rnn.py:82: UserWarning: dropout option adds dropout after all but last recurrent layer, so non-zero dropout expects num_layers greater than 1, but got dropout=0.5 and num_layers=1\n", "  warnings.warn(\"dropout option adds dropout after all but last \"\n"]}], "source": ["for num_epochs in [2,3,5,7,10,15]:\n", "    print(\"---------------\",num_epochs)\n", "    vocab_size = 30522\n", "    embedding_dim = 128\n", "    n_filters = 128\n", "    filter_sizes = [3,4,5]\n", "    output_dim = 6\n", "    dropout = 0.5\n", "    padding_idx = 0\n", "    input_dim = 6 * metadata_each_dim\n", "    input_dim_metadata = 5\n", "    hidden_dim = 64\n", "    n_layers = 1\n", "    bidirectional = True\n", "\n", "    model = LiarModel(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional).to(DEVICE)\n", "\n", "\n", "    # Define the optimizer and loss function\n", "    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)\n", "    criterion = nn.BCEWithLogitsLoss()\n", "\n", "\n", "    # Record the training process\n", "    Train_acc = []\n", "    Train_loss = []\n", "    Train_macro_f1 = []\n", "    Train_micro_f1 = []\n", "\n", "    Val_acc = []\n", "    Val_loss = []\n", "    Val_macro_f1 = []\n", "    Val_micro_f1 = []\n", "\n", "    def train(num_epochs, model, train_loader, val_loader, optimizer, criterion, model_save):\n", "        epoch_trained = 0\n", "        train_label_all = []\n", "        train_predict_all = []\n", "        val_label_all = []\n", "        val_predict_all = []\n", "        best_valid_loss = float('inf')\n", "\n", "        start_time = time.time()\n", "        for epoch in range(num_epochs):\n", "            epoch_trained += 1\n", "            epoch_start_time = time.time()\n", "            # Training\n", "            model.train()\n", "            train_loss = 0.0\n", "            train_accuracy = 0.0\n", "            for statements, label_onehot, label, metadata_text, metadata_number in train_loader:\n", "                statements = statements.to(DEVICE)\n", "                label_onehot = label_onehot.to(DEVICE)\n", "                label = label.to(DEVICE)\n", "                metadata_text = metadata_text.to(DEVICE)\n", "                metadata_number = metadata_number.to(DEVICE)\n", "\n", "                optimizer.zero_grad()\n", "                outputs = model(statements, metadata_text, metadata_number)\n", "                loss = criterion(outputs, label_onehot)\n", "                loss.backward()\n", "                optimizer.step()\n", "\n", "                train_loss += loss.item()\n", "                _, train_predicted = torch.max(outputs, 1)\n", "                train_accuracy += sum(train_predicted == label)\n", "                train_predict_all += train_predicted.tolist()\n", "                train_label_all += label.tolist()\n", "            train_loss /= len(train_loader)\n", "            train_accuracy /= len(train_loader.dataset)\n", "            train_macro_f1 = f1_score(train_label_all, train_predict_all, average='macro')\n", "            train_micro_f1 = f1_score(train_label_all, train_predict_all, average='micro')\n", "\n", "            Train_acc.append(train_accuracy.tolist())\n", "            Train_loss.append(train_loss)\n", "            Train_macro_f1.append(train_macro_f1)\n", "            Train_micro_f1.append(train_micro_f1)\n", "\n", "            # Validation\n", "            model.eval()\n", "            val_loss = 0.0\n", "            val_accuracy = 0.0\n", "            with torch.no_grad():\n", "                for statements, label_onehot, label, metadata_text, metadata_number in val_loader:\n", "                    statements = statements.to(DEVICE)\n", "                    label_onehot = label_onehot.to(DEVICE)\n", "                    label = label.to(DEVICE)\n", "                    metadata_text = metadata_text.to(DEVICE)\n", "                    metadata_number = metadata_number.to(DEVICE)\n", "\n", "                    val_outputs = model(statements, metadata_text, metadata_number)\n", "                    val_loss += criterion(val_outputs, label_onehot).item()\n", "                    _, val_predicted = torch.max(val_outputs, 1)\n", "                    val_accuracy += sum(val_predicted == label)\n", "                    val_predict_all += val_predicted.tolist()\n", "                    val_label_all += label.tolist()\n", "            val_loss /= len(val_loader)\n", "            val_accuracy /= len(val_loader.dataset)\n", "            val_macro_f1 = f1_score(val_label_all, val_predict_all, average='macro')\n", "            val_micro_f1 = f1_score(val_label_all, val_predict_all, average='micro')\n", "\n", "            Val_acc.append(val_accuracy.tolist())\n", "            Val_loss.append(val_loss)\n", "            Val_macro_f1.append(val_macro_f1)\n", "            Val_micro_f1.append(val_micro_f1)\n", "\n", "            if val_loss < best_valid_loss:\n", "                best_valid_loss = val_loss\n", "                torch.save(model.state_dict(), model_save)\n", "                print(f'***** Best Result Updated at Epoch {epoch_trained}, Val Loss: {val_loss:.4f} *****')\n", "\n", "            # Print the losses and accuracy\n", "            epoch_end_time = time.time()\n", "            epoch_time = epoch_end_time - epoch_start_time\n", "\n", "            print(f\"Epoch [{epoch+1}/{num_epochs}], Time: {epoch_time:.2f}s, Train Loss: {train_loss:.4f}, Train Acc: {train_accuracy:.4f}, Train F1 Macro: {train_macro_f1:.4f}, Train F1 Micro: {train_micro_f1:.4f}, Val Loss: {val_loss:.4f}, Val Acc: {val_accuracy:.4f}, Val F1 Macro: {val_macro_f1:.4f}, Val F1 Micro: {val_micro_f1:.4f}\")\n", "\n", "        end_time = time.time()\n", "        training_time = end_time - start_time\n", "        print(f'Total Training Time: {training_time:.2f}s')\n", "\n", "\n", "    train(num_epochs, model, train_loader, val_loader, optimizer, criterion, model_save)\n", "\n", "\n", "    # Evaluate the model on new data\n", "    def test(model, test_loader, model_save):\n", "        model.load_state_dict(torch.load(model_save))\n", "        model.eval()\n", "\n", "        test_label_all = []\n", "        test_predict_all = []\n", "\n", "        test_loss = 0.0\n", "        test_accuracy = 0.0\n", "        with torch.no_grad():\n", "            for statements, label_onehot, label, metadata_text, metadata_number in test_loader:\n", "                statements = statements.to(DEVICE)\n", "                label_onehot = label_onehot.to(DEVICE)\n", "                label = label.to(DEVICE)\n", "                metadata_text = metadata_text.to(DEVICE)\n", "                metadata_number = metadata_number.to(DEVICE)\n", "\n", "                test_outputs = model(statements, metadata_text, metadata_number)\n", "                test_loss += criterion(test_outputs, label_onehot).item()\n", "                _, test_predicted = torch.max(test_outputs, 1)\n", "                \n", "                test_accuracy += sum(test_predicted == label)\n", "                test_predict_all += test_predicted.tolist()\n", "                test_label_all += label.tolist()\n", "\n", "        test_loss /= len(test_loader)\n", "        test_accuracy /= len(test_loader.dataset)\n", "        test_macro_f1 = f1_score(test_label_all, test_predict_all, average='macro')\n", "        test_micro_f1 = f1_score(test_label_all, test_predict_all, average='micro')\n", "\n", "        print(f'Test Loss: {test_loss:.4f}, Test Acc: {test_accuracy:.4f}, Test F1 Macro: {test_macro_f1:.4f}, Test F1 Micro: {test_micro_f1:.4f}')\n", "\n", "\n", "    test(model, test_loader, model_save)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["torch.cuda.empty_cache()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class FuzzyLayer(nn.<PERSON><PERSON><PERSON>):\n", "    def __init__(self, input_dim, membership_num):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        # input_dim: feature number of the dataset\n", "        # membership_num: number of membership function, also known as the class number\n", "\n", "        self.input_dim = input_dim\n", "        self.membership_num = membership_num\n", "\n", "        self.membership_miu = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        self.membership_sigma = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "\n", "        nn.init.xavier_uniform_(self.membership_miu)\n", "        nn.init.ones_(self.membership_sigma)\n", "\n", "    def forward(self, input_seq):\n", "        batch_size = input_seq.size()[0]\n", "        input_seq_exp = input_seq.unsqueeze(1).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_miu_exp = self.membership_miu.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_sigma_exp = self.membership_sigma.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "\n", "        fuzzy_membership = torch.mean(torch.exp((-1 / 2) * ((input_seq_exp - membership_miu_exp) / membership_sigma_exp) ** 2), dim=-1)\n", "        # print(fuzzy_membership.shape)  #torch.Size([32, 6])\n", "        return fuzzy_membership\n", "\n", "## senti batch_size = 128\n", "class SentAttNet(nn.Module):\n", "    def __init__(self,  hidden_size=50):\n", "        super(SentAttNet, self).__init__()\n", "        self.sent_weight = nn.Parameter(torch.Tensor(2 * hidden_size, 10))\n", "        self.sent_bias = nn.Parameter(torch.Tensor(10))\n", "        self.context_weight = nn.Parameter(torch.Tensor(10, 1))\n", "        self.gru1 = nn.GRU(510, hidden_size, bidirectional=True)  #将GRU改成LSTM\n", "        self.gru2 = nn.GRU(509, hidden_size, bidirectional=True)\n", "        self.gru3 = nn.GRU(508, hidden_size, bidirectional=True)\n", "        self.gru4 = nn.GRU(58, hidden_size, bidirectional=True)\n", "        self.gru5 = nn.GRU(57, hidden_size, bidirectional=True)\n", "        self.gru6 = nn.GRU(56, hidden_size, bidirectional=True)\n", "        self._create_weights(mean=0.0, std=0.05)\n", "        self.hidden_size = hidden_size\n", "    def _create_weights(self, mean=0.0, std=0.05):\n", "        self.sent_weight.data.normal_(mean, std)\n", "        self.context_weight.data.normal_(mean, std)\n", "\n", "    def forward(self, input, hidden_state):\n", "#         f_output, h_output = self.gru(input, hidden_state)\n", "        input.to('cuda')\n", "        hidden_state.to('cuda')\n", "        input_shape = input.size()\n", "        if(input_shape[-1]==510):\n", "            f_output, h_output = self.gru1(input, hidden_state)\n", "        elif(input_shape[-1]==509):\n", "            f_output, h_output = self.gru2(input, hidden_state)\n", "        elif(input_shape[-1]==508):\n", "            f_output, h_output = self.gru3(input, hidden_state)\n", "#         elif(input_shape[-1]==58):\n", "#             f_output, h_output = self.gru4(input, hidden_state)\n", "#         elif(input_shape[-1]==57):\n", "#             f_output, h_output = self.gru5(input, hidden_state)\n", "#         else:\n", "# #             print(input_shape)\n", "#             f_output, h_output = self.gru6(input, hidden_state)\n", "        output = matrix_mul(f_output, self.sent_weight, self.context_weight, self.sent_bias)\n", "        output = F.softmax(output)\n", "        output = element_wise_mul(output.permute(1,0,2), f_output.permute(1,0,2))\n", "\n", "        return output, h_output\n", "    \n", "def element_wise_mul(input1, input2):\n", "    feature_list = []\n", "    for feature_1, feature_2 in zip(input1, input2):\n", "        feature = (feature_1 * feature_2).sum(dim=0)\n", "        feature_list.append(feature)\n", "\n", "    output = torch.stack(feature_list, dim=0)\n", "    return output\n", "\n", "def matrix_mul(input, weight, context_weight,  bias=False):\n", "    input = torch.matmul(input, weight)\n", "    input = input + bias\n", "    input = torch.tanh(input)\n", "    input = torch.matmul(input, context_weight)\n", "\n", "    return input\n", "\n", "class TextCNN(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.convs = nn.ModuleList([\n", "                                    nn.Conv1d(in_channels = embedding_dim, \n", "                                              out_channels = n_filters, \n", "                                              kernel_size = fs)\n", "                                    for fs in filter_sizes\n", "                                    ])      \n", "        \n", "        self.fc = nn.Linear(len(filter_sizes) * n_filters, output_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "        self.batch_size = 128\n", "        self.sent_hidden_size = 50\n", "        self._init_hidden_state()\n", "        self.sent = SentAttNet( hidden_size=50)\n", "        \n", "    def _init_hidden_state(self, last_batch_size=None):\n", "        if last_batch_size:\n", "            batch_size = last_batch_size\n", "        else:\n", "            batch_size = self.batch_size\n", "        self.sent_hidden_state = torch.zeros(2, batch_size, self.sent_hidden_size)\n", "        if torch.cuda.is_available():\n", "            self.sent_hidden_state = self.sent_hidden_state.cuda()\n", "    \n", "    def forward(self, text):\n", "        #text = [batch size, sent len]\n", "        embedded = self.embedding(text)\n", "        #embedded = [batch size, sent len, emb dim]\n", "\n", "        embedded = embedded.permute(0, 2, 1)\n", "        #embedded = [batch size, emb dim, sent len]\n", "#         print(\"embedded:\",embedded.shape)\n", "        conved = [F.relu(conv(embedded)) for conv in self.convs]\n", "        #conved_n = [batch size, n_filters, sent len - filter_sizes[n] + 1]\n", "#          因为conved是list，GRU因为时间步需要tensor\n", "#         for i in range(len(conved)):\n", "#             print(\"i:\",conved[i].shape)\n", "\n", "        # conv1 = []\n", "        # for conv in conved:\n", "        #     conv , self.sent_hidden_state= self.sent(conv , self.sent_hidden_state)\n", "        #     conv1.append(conv)\n", "        \n", "        pooled = [F.max_pool1d(conv, conv.shape[2]).squeeze(2) for conv in conved]\n", "        #pooled_n = [batch size, n_filters]\n", "        cat = self.dropout(torch.cat(pooled, dim = 1))\n", "        #cat = [batch size, n_filters * len(filter_sizes)]\n", "\n", "        return self.fc(cat)\n", "\n", "class CNNBiLSTM(nn.Module):\n", "    def __init__(self, input_dim, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Linear(input_dim, embedding_dim)\n", "        self.conv = nn.Conv1d(in_channels=embedding_dim, out_channels=32, kernel_size=1)\n", "        self.rnn = nn.LSTM(32, \n", "                           hidden_dim, \n", "                           num_layers=n_layers, \n", "                           bidirectional=bidirectional, \n", "                           dropout=dropout)\n", "        self.fc = nn.Linear(hidden_dim * 2, output_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "    \n", "    def forward(self, metadata):\n", "        #metadata = [batch size, metadata dim]\n", "\n", "        embedded = self.dropout(self.embedding(metadata))\n", "        #embedded = [batch size, metadata dim, emb dim]\n", "\n", "        embedded = torch.reshape(embedded, (metadata.size(0), 128, 1))\n", "\n", "        conved = <PERSON>.relu(self.conv(embedded))\n", "        #conved = [batch size, n_filters, metadata dim - filter_sizes[n] + 1]\n", "        conved = torch.reshape(conved, (metadata.size(0), 32))\n", "        outputs, (hidden, cell) = self.rnn(conved)\n", "        \n", "        #outputs = [metadata dim - filter_sizes[n] + 1, batch size, hid dim * num directions]\n", "        #hidden = [num layers * num directions, batch size, hid dim]\n", "        #cell = [num layers * num directions, batch size, hid dim]\n", "\n", "        #concat the final forward (hidden[-2,:,:]) and backward (hidden[-1,:,:]) hidden layers\n", "        #and apply dropout\n", "        # hidden = self.dropout(torch.cat((hidden[-1,:], hidden[0,:]), dim = -1))\n", "        #hidden = [batch size, hid dim * num directions]\n", "\n", "        return self.fc(outputs)\n", "\n", "\n", "class LiarModel(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional):\n", "        super().__init__()\n", "\n", "        self.textcnn = TextCNN(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.textcnn2 = TextCNN(vocab_size, input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.cnn_bilstm = CNNBiLSTM(input_dim_metadata, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout)\n", "        self.fuzzy = FuzzyLayer(output_dim, output_dim)\n", "        self.fuse = nn.Linear(output_dim * 4, output_dim)\n", "    \n", "    def forward(self, text, metadata_text, metadata_number):\n", "        #text = [batch size, sent len]\n", "        #metadata = [batch size, metadata dim]\n", "\n", "        text_output = self.textcnn(text)\n", "        metadata_output_text = self.textcnn2(metadata_text)\n", "        metadata_output_number = self.cnn_bilstm(metadata_number)\n", "        metadata_output_fuzzy = self.fuzzy(metadata_output_number)\n", "\n", "        fused_output = self.fuse(torch.cat((text_output, metadata_output_text, metadata_output_number, metadata_output_fuzzy), dim=1))\n", "\n", "        return fused_output\n", "\n", "\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "TEXTGCN", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}