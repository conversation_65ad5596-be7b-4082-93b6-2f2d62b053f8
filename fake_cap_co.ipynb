{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PyTorch Version : 2.1.0+cu121\n", "cuda\n"]}, {"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'D:/code/HLAN/FAKE/liar_dataset/train.tsv'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 51\u001b[0m\n\u001b[0;32m     48\u001b[0m label_map \u001b[38;5;241m=\u001b[39m {\u001b[38;5;241m0\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpants-fire\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;241m1\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfalse\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;241m2\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbarely-true\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;241m3\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhalf-true\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;241m4\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmostly-true\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;241m5\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtrue\u001b[39m\u001b[38;5;124m'\u001b[39m}\n\u001b[0;32m     49\u001b[0m label_convert \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpants-fire\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m0\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfalse\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m1\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbarely-true\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m2\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhalf-true\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m3\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmostly-true\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;241m4\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtrue\u001b[39m\u001b[38;5;124m'\u001b[39m:\u001b[38;5;241m5\u001b[39m}\n\u001b[1;32m---> 51\u001b[0m train_data \u001b[38;5;241m=\u001b[39m \u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_csv\u001b[49m\u001b[43m(\u001b[49m\u001b[43mworksapce\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mtrain.tsv\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msep\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;130;43;01m\\t\u001b[39;49;00m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnames\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mcol\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     52\u001b[0m test_data \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_csv(worksapce \u001b[38;5;241m+\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtest.tsv\u001b[39m\u001b[38;5;124m'\u001b[39m, sep \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\t\u001b[39;00m\u001b[38;5;124m'\u001b[39m, names \u001b[38;5;241m=\u001b[39m col)\n\u001b[0;32m     53\u001b[0m val_data \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_csv(worksapce \u001b[38;5;241m+\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mvalid.tsv\u001b[39m\u001b[38;5;124m'\u001b[39m, sep \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\t\u001b[39;00m\u001b[38;5;124m'\u001b[39m, names \u001b[38;5;241m=\u001b[39m col)\n", "File \u001b[1;32mE:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\pandas\\io\\parsers\\readers.py:1026\u001b[0m, in \u001b[0;36mread_csv\u001b[1;34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, date_format, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, encoding_errors, dialect, on_bad_lines, delim_whitespace, low_memory, memory_map, float_precision, storage_options, dtype_backend)\u001b[0m\n\u001b[0;32m   1013\u001b[0m kwds_defaults \u001b[38;5;241m=\u001b[39m _refine_defaults_read(\n\u001b[0;32m   1014\u001b[0m     dialect,\n\u001b[0;32m   1015\u001b[0m     delimiter,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1022\u001b[0m     dtype_backend\u001b[38;5;241m=\u001b[39mdtype_backend,\n\u001b[0;32m   1023\u001b[0m )\n\u001b[0;32m   1024\u001b[0m kwds\u001b[38;5;241m.\u001b[39mupdate(kwds_defaults)\n\u001b[1;32m-> 1026\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_read\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mE:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\pandas\\io\\parsers\\readers.py:620\u001b[0m, in \u001b[0;36m_read\u001b[1;34m(filepath_or_buffer, kwds)\u001b[0m\n\u001b[0;32m    617\u001b[0m _validate_names(kwds\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnames\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m))\n\u001b[0;32m    619\u001b[0m \u001b[38;5;66;03m# Create the parser.\u001b[39;00m\n\u001b[1;32m--> 620\u001b[0m parser \u001b[38;5;241m=\u001b[39m TextFileReader(filepath_or_buffer, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwds)\n\u001b[0;32m    622\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m chunksize \u001b[38;5;129;01mor\u001b[39;00m iterator:\n\u001b[0;32m    623\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m parser\n", "File \u001b[1;32mE:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\pandas\\io\\parsers\\readers.py:1620\u001b[0m, in \u001b[0;36mTextFileReader.__init__\u001b[1;34m(self, f, engine, **kwds)\u001b[0m\n\u001b[0;32m   1617\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhas_index_names\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m kwds[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhas_index_names\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[0;32m   1619\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles: IOHandles \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m-> 1620\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_engine \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_engine\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mengine\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mE:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\pandas\\io\\parsers\\readers.py:1880\u001b[0m, in \u001b[0;36mTextFileReader._make_engine\u001b[1;34m(self, f, engine)\u001b[0m\n\u001b[0;32m   1878\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m mode:\n\u001b[0;32m   1879\u001b[0m         mode \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m-> 1880\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles \u001b[38;5;241m=\u001b[39m \u001b[43mget_handle\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1881\u001b[0m \u001b[43m    \u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1882\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1883\u001b[0m \u001b[43m    \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mencoding\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1884\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcompression\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcompression\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1885\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmemory_map\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmemory_map\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1886\u001b[0m \u001b[43m    \u001b[49m\u001b[43mis_text\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mis_text\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1887\u001b[0m \u001b[43m    \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mencoding_errors\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstrict\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1888\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstorage_options\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1889\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1890\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m   1891\u001b[0m f \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles\u001b[38;5;241m.\u001b[39mhandle\n", "File \u001b[1;32mE:\\liuhui\\ljl\\FAKE\\FAKE\\venv\\lib\\site-packages\\pandas\\io\\common.py:873\u001b[0m, in \u001b[0;36mget_handle\u001b[1;34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[0m\n\u001b[0;32m    868\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(handle, \u001b[38;5;28mstr\u001b[39m):\n\u001b[0;32m    869\u001b[0m     \u001b[38;5;66;03m# Check whether the filename is to be opened in binary mode.\u001b[39;00m\n\u001b[0;32m    870\u001b[0m     \u001b[38;5;66;03m# Binary mode does not support 'encoding' and 'newline'.\u001b[39;00m\n\u001b[0;32m    871\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mencoding \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mmode:\n\u001b[0;32m    872\u001b[0m         \u001b[38;5;66;03m# Encoding\u001b[39;00m\n\u001b[1;32m--> 873\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[0;32m    874\u001b[0m \u001b[43m            \u001b[49m\u001b[43mhandle\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    875\u001b[0m \u001b[43m            \u001b[49m\u001b[43mioargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    876\u001b[0m \u001b[43m            \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mioargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    877\u001b[0m \u001b[43m            \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    878\u001b[0m \u001b[43m            \u001b[49m\u001b[43mnewline\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m    879\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    880\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    881\u001b[0m         \u001b[38;5;66;03m# Binary mode\u001b[39;00m\n\u001b[0;32m    882\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mopen\u001b[39m(handle, ioargs\u001b[38;5;241m.\u001b[39mmode)\n", "\u001b[1;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: 'D:/code/HLAN/FAKE/liar_dataset/train.tsv'"]}], "source": ["# ===============================\n", "# Title: Fuzzy Deep Hybrid Network (FDHN) for Fake News Detection\n", "# Author: <PERSON>\n", "# Create Date: Apr 21, 2023\n", "# Revision Date: Otc 23, 2023\n", "# Github: https://github.com/chengxuphd/FDHN\n", "# DOI: https://doi.org/10.1145/3628797.3628971\n", "# Description: This is the source code of FDHN model. The dataset used in this code is LIAR (https://www.cs.ucsb.edu/~william/data/liar_dataset.zip).\n", "# Notes: This is code for the TC+TC+CB+FZ experiment.\n", "# ===============================\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import torch.utils.data as data\n", "from sklearn.metrics import f1_score\n", "from torch.utils.data import DataLoader\n", "from transformers import BertTokenizer, BertForSequenceClassification\n", "\n", "# Fixing the randomness of CUDA.\n", "torch.backends.cudnn.deterministic = True\n", "torch.backends.cudnn.benchmark = False\n", "\n", "np.random.seed(42)\n", "torch.manual_seed(42)\n", "\n", "DEVICE = torch.device(\"cuda\") if torch.cuda.is_available() else torch.device(\"cpu\")\n", "print(\"PyTorch Version : {}\".format(torch.__version__))\n", "print(DEVICE)\n", "\n", "\n", "worksapce = 'D:/code/HLAN/FAKE/liar_dataset/'\n", "model_save = 'TC+TC+CB+FZ.pt'\n", "model_name = 'TC+TC+CB+FZ'\n", "num_epochs = 10\n", "batch_size = 32\n", "learning_rate = 1e-3\n", "num_classes = 6\n", "padding_idx = 0\n", "metadata_each_dim = 10\n", "\n", "\n", "col = ['id', 'label', 'statement', 'subject', 'speaker', 'job_title', 'state_info', 'party_affiliation', 'barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts', 'context']\n", "\n", "label_map = {0: 'pants-fire', 1: 'false', 2: 'barely-true', 3: 'half-true', 4: 'mostly-true', 5: 'true'}\n", "label_convert = {'pants-fire': 0, 'false': 1, 'barely-true': 2, 'half-true': 3, 'mostly-true': 4, 'true':5}\n", "\n", "train_data = pd.read_csv(worksapce + 'train.tsv', sep = '\\t', names = col)\n", "test_data = pd.read_csv(worksapce + 'test.tsv', sep = '\\t', names = col)\n", "val_data = pd.read_csv(worksapce + 'valid.tsv', sep = '\\t', names = col)\n", "\n", "# Replace NaN values with 'NaN'\n", "train_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = train_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)\n", "train_data.fillna('NaN', inplace=True)\n", "\n", "test_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = test_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)\n", "test_data.fillna('NaN', inplace=True)\n", "\n", "val_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = val_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)\n", "val_data.fillna('NaN', inplace=True)\n", "\n", "\n", "\n", "\n", "def textProcess(input_text, max_length = -1):\n", "    tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')\n", "    if max_length == -1:\n", "        tokens = tokenizer(input_text, truncation=True, padding=True)\n", "    else:\n", "        tokens = tokenizer(input_text, truncation=True, padding='max_length', max_length=max_length)\n", "    return tokens\n", "\n", "\n", "\n", "# Define a custom dataset for loading the data\n", "class LiarDataset(data.Dataset):\n", "    def __init__(self, data_df, statement, label_onehot, label, subject, speaker, job_title, state_info,\n", "                     party_affiliation, barely_true_counts, false_counts, half_true_counts, mostly_true_counts,\n", "                    pants_on_fire_counts, context):\n", "        self.data_df = data_df\n", "        self.statement = statement\n", "        self.label_onehot = label_onehot\n", "        self.label = label\n", "        self.metadata_text = torch.cat((subject.int(), speaker.int(), job_title.int(), state_info.int(), party_affiliation.int(), \n", "                                   context.int()), dim=-1)\n", "        self.metadata_number = torch.cat((torch.tensor(barely_true_counts, dtype=torch.float).unsqueeze(1), torch.tensor(false_counts, dtype=torch.float).unsqueeze(1), \n", "                                   torch.tensor(half_true_counts, dtype=torch.float).unsqueeze(1), torch.tensor(mostly_true_counts, dtype=torch.float).unsqueeze(1), \n", "                                   torch.tensor(pants_on_fire_counts, dtype=torch.float).unsqueeze(1)), dim=-1)\n", "\n", "\n", "        \n", "    def __len__(self):\n", "        return len(self.data_df)\n", "    \n", "    def __getitem__(self, idx):\n", "        statement = self.statement[idx]\n", "        label_onehot = self.label_onehot[idx]\n", "        label = self.label[idx]\n", "        metadata_text = self.metadata_text[idx]\n", "        metadata_number = self.metadata_number[idx]\n", "        return statement, label_onehot, label, metadata_text, metadata_number\n", "\n", "\n", "# Define the data loaders for training and validation\n", "train_text = torch.tensor(textProcess(train_data['statement'].tolist())['input_ids'])\n", "train_label = torch.nn.functional.one_hot(torch.tensor(train_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)\n", "train_subject = torch.tensor(textProcess(train_data['subject'].tolist(), metadata_each_dim)['input_ids'])\n", "train_speaker = torch.tensor(textProcess(train_data['speaker'].tolist(), metadata_each_dim)['input_ids'])\n", "train_job_title = torch.tensor(textProcess(train_data['job_title'].tolist(), metadata_each_dim)['input_ids'])\n", "train_state_info = torch.tensor(textProcess(train_data['state_info'].tolist(), metadata_each_dim)['input_ids'])\n", "train_party_affiliation = torch.tensor(textProcess(train_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])\n", "train_context = torch.tensor(textProcess(train_data['context'].tolist(), metadata_each_dim)['input_ids'])\n", "\n", "train_dataset = LiarDataset(train_data, train_text, train_label, torch.tensor(train_data['label'].replace(label_convert)), \n", "                            train_subject, train_speaker, train_job_title, \n", "                            train_state_info, train_party_affiliation, \n", "                            train_data['barely_true_counts'].tolist(), train_data['false_counts'].tolist(), \n", "                            train_data['half_true_counts'].tolist(), train_data['mostly_true_counts'].tolist(), \n", "                            train_data['pants_on_fire_counts'].tolist(), train_context)\n", "train_loader = data.DataLoader(train_dataset, batch_size=batch_size, shuffle=True)\n", "\n", "val_text = torch.tensor(textProcess(val_data['statement'].tolist())['input_ids'])\n", "val_label = torch.nn.functional.one_hot(torch.tensor(val_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)\n", "val_subject = torch.tensor(textProcess(val_data['subject'].tolist(), metadata_each_dim)['input_ids'])\n", "val_speaker = torch.tensor(textProcess(val_data['speaker'].tolist(), metadata_each_dim)['input_ids'])\n", "val_job_title = torch.tensor(textProcess(val_data['job_title'].tolist(), metadata_each_dim)['input_ids'])\n", "val_state_info = torch.tensor(textProcess(val_data['state_info'].tolist(), metadata_each_dim)['input_ids'])\n", "val_party_affiliation = torch.tensor(textProcess(val_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])\n", "val_context = torch.tensor(textProcess(val_data['context'].tolist(), metadata_each_dim)['input_ids'])\n", "\n", "val_dataset = LiarDataset(val_data, val_text, val_label, torch.tensor(val_data['label'].replace(label_convert)),\n", "                          val_subject, val_speaker, val_job_title, \n", "                          val_state_info, val_party_affiliation, \n", "                          val_data['barely_true_counts'].tolist(), val_data['false_counts'].tolist(), \n", "                          val_data['half_true_counts'].tolist(), val_data['mostly_true_counts'].tolist(), \n", "                          val_data['pants_on_fire_counts'].tolist(), val_context)\n", "val_loader = data.DataLoader(val_dataset, batch_size=batch_size)\n", "\n", "test_text = torch.tensor(textProcess(test_data['statement'].tolist())['input_ids'])\n", "test_label = torch.nn.functional.one_hot(torch.tensor(test_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)\n", "test_subject = torch.tensor(textProcess(test_data['subject'].tolist(), metadata_each_dim)['input_ids'])\n", "test_speaker = torch.tensor(textProcess(test_data['speaker'].tolist(), metadata_each_dim)['input_ids'])\n", "test_job_title = torch.tensor(textProcess(test_data['job_title'].tolist(), metadata_each_dim)['input_ids'])\n", "test_state_info = torch.tensor(textProcess(test_data['state_info'].tolist(), metadata_each_dim)['input_ids'])\n", "test_party_affiliation = torch.tensor(textProcess(test_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])\n", "test_context = torch.tensor(textProcess(test_data['context'].tolist(), metadata_each_dim)['input_ids'])\n", "\n", "test_dataset = LiarDataset(test_data, test_text, test_label, torch.tensor(test_data['label'].replace(label_convert)),\n", "                          test_subject, test_speaker, test_job_title, \n", "                          test_state_info, test_party_affiliation, \n", "                          test_data['barely_true_counts'].tolist(), test_data['false_counts'].tolist(), \n", "                          test_data['half_true_counts'].tolist(), test_data['mostly_true_counts'].tolist(), \n", "                          test_data['pants_on_fire_counts'].tolist(), test_context)\n", "test_loader = data.DataLoader(test_dataset, batch_size=batch_size)\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### multihead"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["class MultiHeadAttention(nn.Module):\n", " \n", "    def __init__(self, query_dim, key_dim, num_units, num_heads):\n", " \n", "        super().__init__()\n", "        self.num_units = num_units\n", "        self.num_heads = num_heads\n", "        self.key_dim = key_dim\n", " \n", "        self.W_query = nn.Linear(in_features=query_dim, out_features=num_units, bias=False)\n", "        self.W_key = nn.Linear(in_features=key_dim, out_features=num_units, bias=False)\n", "        self.W_value = nn.Linear(in_features=key_dim, out_features=num_units, bias=False)\n", " \n", "    def forward(self, query, key, mask=None):\n", "        querys = self.W_query(query) \n", "        keys = self.W_key(key)  \n", "        values = self.W_value(key)\n", " \n", "        split_size = self.num_units // self.num_heads\n", "        querys = torch.stack(torch.split(querys, split_size, dim=2), dim=0)  \n", "        keys = torch.stack(torch.split(keys, split_size, dim=2), dim=0)\n", "        values = torch.stack(torch.split(values, split_size, dim=2), dim=0)  \n", "\n", "        scores = torch.matmul(querys, keys.transpose(2, 3)) \n", "        scores = scores / (self.key_dim ** 0.5)\n", " \n", "        if mask is not None:\n", "            mask = mask.unsqueeze(1).unsqueeze(0).repeat(self.num_heads,1,querys.shape[2],1)\n", "            scores = scores.masked_fill(mask, -np.inf)\n", "        scores = <PERSON>.softmax(scores, dim=3)\n", "        out = torch.matmul(scores, values)  # [h, N, T_q, num_units/h]\n", "        out = torch.cat(torch.split(out, 1, dim=0), dim=3).squeeze(0)  # [N, T_q, num_units]\n", " \n", "        return out,scores"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### main"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [], "source": ["class FuzzyLayer1(nn.<PERSON><PERSON><PERSON>):  #广义钟形隶属函数\n", "    def __init__(self, input_dim, membership_num):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>1, self).__init__()\n", "\n", "        # input_dim: feature number of the dataset\n", "        # membership_num: number of membership function, also known as the class number\n", "\n", "        self.input_dim = input_dim\n", "        self.membership_num = membership_num\n", "\n", "        self.membership_miu = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        self.membership_a = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        self.membership_b = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "\n", "        nn.init.xavier_uniform_(self.membership_miu)\n", "        nn.init.ones_(self.membership_a)\n", "        nn.init.ones_(self.membership_b)\n", "\n", "    def forward(self, input_seq):\n", "        batch_size = input_seq.size()[0]\n", "        input_seq_exp = input_seq.unsqueeze(1).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_miu_exp = self.membership_miu.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_a_exp = self.membership_a.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_b_exp = self.membership_b.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "\n", "        # fuzzy_membership = torch.mean(torch.exp((-1 / 2) * ((input_seq_exp - membership_miu_exp) / membership_sigma_exp) ** 2), dim=-1)\n", "        # print(fuzzy_membership.shape)  #torch.Size([32, 6])\n", "        fuzzy_membership = torch.mean(1/1+(torch.abs((input_seq_exp - membership_miu_exp)/membership_a_exp)) ** membership_b_exp, dim=-1)\n", "        return fuzzy_membership\n", "\n", "class FuzzyLayer(nn.<PERSON><PERSON><PERSON>):\n", "    def __init__(self, input_dim, membership_num):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        # input_dim: feature number of the dataset\n", "        # membership_num: number of membership function, also known as the class number\n", "\n", "        self.input_dim = input_dim\n", "        self.membership_num = membership_num\n", "\n", "        self.membership_miu = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "        self.membership_sigma = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)\n", "\n", "        nn.init.xavier_uniform_(self.membership_miu)\n", "        nn.init.ones_(self.membership_sigma)\n", "\n", "    def forward(self, input_seq):\n", "        batch_size = input_seq.size()[0]\n", "        input_seq_exp = input_seq.unsqueeze(1).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_miu_exp = self.membership_miu.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "        membership_sigma_exp = self.membership_sigma.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)\n", "\n", "        fuzzy_membership = torch.mean(torch.exp((-1 / 2) * ((input_seq_exp - membership_miu_exp) / membership_sigma_exp) ** 2), dim=-1)\n", "        return fuzzy_membership\n", "\n", "\n", "\n", "class TextCNN(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "\n", "        self.convs = nn.ModuleList([\n", "                                    nn.Conv1d(in_channels = embedding_dim, \n", "                                              out_channels = n_filters, \n", "                                              kernel_size = fs)\n", "                                    for fs in filter_sizes\n", "                                    ])\n", "        self.fc = nn.Linear(len(filter_sizes) * n_filters, output_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "    \n", "    def forward(self, text):\n", "        #text = [batch size, sent len]\n", "        embedded = self.embedding(text)\n", "        \n", "        #embedded = [batch size, sent len, emb dim]\n", "\n", "\n", "        embedded = embedded.permute(0, 2, 1)\n", "        #embedded = [batch size, emb dim, sent len]\n", "\n", "        conved = [F.relu(conv(embedded)) for conv in self.convs]\n", "        #conved_n = [batch size, n_filters, sent len - filter_sizes[n] + 1]\n", "\n", "        pooled = [F.max_pool1d(conv, conv.shape[2]).squeeze(2) for conv in conved]\n", "        #pooled_n = [batch size, n_filters]\n", "\n", "        cat = self.dropout(torch.cat(pooled, dim = 1))\n", "        #cat = [batch size, n_filters * len(filter_sizes)]\n", "\n", "        return self.fc(cat)\n", "\n", "class CNNBiLSTM(nn.Module):\n", "    def __init__(self, input_dim, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Linear(input_dim, embedding_dim)\n", "        self.conv = nn.Conv1d(in_channels=embedding_dim, out_channels=32, kernel_size=1)\n", "        self.rnn = nn.LSTM(32, \n", "                           hidden_dim, \n", "                           num_layers=n_layers, \n", "                           bidirectional=bidirectional, \n", "                           dropout=dropout)\n", "        self.fc = nn.Linear(hidden_dim * 2, output_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "    \n", "    def forward(self, metadata):\n", "        #metadata = [batch size, metadata dim]\n", "\n", "        embedded = self.dropout(self.embedding(metadata))\n", "        #embedded = [batch size, metadata dim, emb dim]\n", "\n", "        embedded = torch.reshape(embedded, (metadata.size(0), 128, 1))\n", "\n", "        conved = <PERSON>.relu(self.conv(embedded))\n", "        #conved = [batch size, n_filters, metadata dim - filter_sizes[n] + 1]\n", "\n", "        conved = torch.reshape(conved, (metadata.size(0), 32))\n", "\n", "        outputs, (hidden, cell) = self.rnn(conved)\n", "        #outputs = [metadata dim - filter_sizes[n] + 1, batch size, hid dim * num directions]\n", "        #hidden = [num layers * num directions, batch size, hid dim]\n", "        #cell = [num layers * num directions, batch size, hid dim]\n", "\n", "        #concat the final forward (hidden[-2,:,:]) and backward (hidden[-1,:,:]) hidden layers\n", "        #and apply dropout\n", "        # hidden = self.dropout(torch.cat((hidden[-1,:], hidden[0,:]), dim = -1))\n", "        #hidden = [batch size, hid dim * num directions]\n", "\n", "        return self.fc(outputs)\n", "\n", "class DualText(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, input_dim,n_filters, filter_sizes, output_dim, dropout, pad_idx):\n", "        super().__init__()\n", "\n", "        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)\n", "        self.embedding1 = nn.Embedding(vocab_size, input_dim, padding_idx = pad_idx)\n", "        num_units = 32\n", "        self._attention_T_to_Ti = MultiHeadAttention(128,60,num_units,1)\n", "        self._attention_Ti_to_T = MultiHeadAttention(60,128,num_units,1)\n", "        # text \n", "        self.convs = nn.ModuleList([\n", "                                    nn.Conv1d(in_channels = num_units, \n", "                                              out_channels = n_filters, \n", "                                              kernel_size = fs)\n", "                                    for fs in filter_sizes\n", "                                    ])\n", "        self.fc = nn.Linear(len(filter_sizes) * n_filters, output_dim)\n", "        self.dropout = nn.Dropout(dropout)\n", "        \n", "\n", "    def forward(self, text, textual):\n", "        #text = [batch size, sent len]\n", "\n", "        embedded = self.embedding(text)\n", "        metadata_embedded = self.embedding1(textual)\n", "\n", "        att_T_Ti,_=self._attention_T_to_Ti(embedded,metadata_embedded)\n", "        att_Ti_T,_=self._attention_Ti_to_T(metadata_embedded,embedded)\n", "        # print(att_T_Ti.shape, att_Ti_T.shape) #torch.Size([32, 512, 32]) torch.Size([32, 60, 32])\n", "\n", "        #text\n", "        att_T_Ti = att_T_Ti.permute(0, 2, 1)\n", "        att_Ti_T = att_Ti_T.permute(0, 2, 1)\n", "        #embedded = [batch size, emb dim, sent len]\n", "\n", "        conved = [F.relu(conv(att_T_Ti)) for conv in self.convs]\n", "        meta_conved = [F.relu(conv(att_Ti_T)) for conv in self.convs]\n", "        #conved_n = [batch size, n_filters, sent len - filter_sizes[n] + 1]\n", "\n", "        pooled = [F.max_pool1d(conv, conv.shape[2]).squeeze(2) for conv in conved]\n", "        meta_pooled = [F.max_pool1d(conv, conv.shape[2]).squeeze(2) for conv in meta_conved]\n", "        #pooled_n = [batch size, n_filters]\n", "\n", "        cat = self.dropout(torch.cat(pooled, dim = 1))\n", "        meta_cat = self.dropout(torch.cat(meta_pooled, dim = 1))\n", "        #cat = [batch size, n_filters * len(filter_sizes)]\n", "        text_output = self.fc(cat)\n", "        meta_output = self.fc(meta_cat)\n", "        return text_output, meta_output\n", "\n", "class LiarModel(nn.Module):\n", "    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional):\n", "        super().__init__()\n", "\n", "        self.textcnn = TextCNN(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.textcnn2 = TextCNN(vocab_size, input_dim,n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "        self.cnn_bilstm = CNNBiLSTM(input_dim_metadata, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout)\n", "        self.fuzzy = FuzzyLayer(output_dim, output_dim)\n", "        self.fuse = nn.Linear(output_dim * 4, output_dim)\n", "        self.dualtext = DualText(vocab_size, embedding_dim,input_dim,n_filters, filter_sizes, output_dim, dropout, padding_idx)\n", "\n", "    def forward(self, text, metadata_text, metadata_number):\n", "        #text = [batch size, sent len]\n", "        #metadata = [batch size, metadata dim]\n", "\n", "        # text_output = self.textcnn(text)\n", "        # metadata_output_text = self.textcnn2(metadata_text)\n", "        metadata_output_number = self.cnn_bilstm(metadata_number)\n", "        metadata_output_fuzzy = self.fuzzy(metadata_output_number)\n", "        text_output,metadata_output_text = self.dualtext(text, metadata_text)\n", "        fused_output = self.fuse(torch.cat((text_output, metadata_output_text, metadata_output_number, metadata_output_fuzzy), dim=1))\n", "\n", "        return fused_output"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [], "source": ["num_epochs = 10"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\TEXTGCN\\lib\\site-packages\\torch\\nn\\modules\\rnn.py:82: UserWarning: dropout option adds dropout after all but last recurrent layer, so non-zero dropout expects num_layers greater than 1, but got dropout=0.5 and num_layers=1\n", "  warnings.warn(\"dropout option adds dropout after all but last \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["***** Best Result Updated at Epoch 1, Val Loss: 0.3861 *****\n", "Epoch [1/5], Time: 3.59s, Train Loss: 0.4344, Train Acc: 0.2939, Train F1 Macro: 0.2462, Train F1 Micro: 0.2939, Val Loss: 0.3861, Val Acc: 0.4361, Val F1 Macro: 0.3798, Val F1 Micro: 0.4361\n", "***** Best Result Updated at Epoch 2, Val Loss: 0.3844 *****\n", "Epoch [2/5], Time: 3.28s, Train Loss: 0.3731, Train Acc: 0.4341, Train F1 Macro: 0.3346, Train F1 Micro: 0.3640, Val Loss: 0.3844, Val Acc: 0.4463, Val F1 Macro: 0.4141, Val F1 Micro: 0.4412\n", "Epoch [3/5], Time: 3.22s, Train Loss: 0.3523, Train Acc: 0.4686, Train F1 Macro: 0.3776, Train F1 Micro: 0.3989, Val Loss: 0.4559, Val Acc: 0.4346, Val F1 Macro: 0.4206, Val F1 Micro: 0.4390\n", "Epoch [4/5], Time: 3.20s, Train Loss: 0.3362, Train Acc: 0.4912, Train F1 Macro: 0.4041, Train F1 Micro: 0.4219, Val Loss: 0.5663, Val Acc: 0.3980, Val F1 Macro: 0.4154, Val F1 Micro: 0.4287\n", "Epoch [5/5], Time: 3.19s, Train Loss: 0.3236, Train Acc: 0.5143, Train F1 Macro: 0.4249, Train F1 Micro: 0.4404, Val Loss: 0.7335, Val Acc: 0.3871, Val F1 Macro: 0.4089, Val F1 Micro: 0.4204\n", "Total Training Time: 16.47s\n", "Test Loss: 0.3620, Test Acc: 0.4436, Test F1 Macro: 0.4465, Test F1 Micro: 0.4436\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\TEXTGCN\\lib\\site-packages\\torch\\nn\\modules\\rnn.py:82: UserWarning: dropout option adds dropout after all but last recurrent layer, so non-zero dropout expects num_layers greater than 1, but got dropout=0.5 and num_layers=1\n", "  warnings.warn(\"dropout option adds dropout after all but last \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["***** Best Result Updated at Epoch 1, Val Loss: 0.3978 *****\n", "Epoch [1/15], Time: 3.17s, Train Loss: 0.4354, Train Acc: 0.2776, Train F1 Macro: 0.2296, Train F1 Micro: 0.2776, Val Loss: 0.3978, Val Acc: 0.4120, Val F1 Macro: 0.3522, Val F1 Micro: 0.4120\n", "***** Best Result Updated at Epoch 2, Val Loss: 0.3679 *****\n", "Epoch [2/15], Time: 3.15s, Train Loss: 0.3833, Train Acc: 0.4155, Train F1 Macro: 0.3140, Train F1 Micro: 0.3466, Val Loss: 0.3679, Val Acc: 0.4603, Val F1 Macro: 0.4078, Val F1 Micro: 0.4361\n", "Epoch [3/15], Time: 3.17s, Train Loss: 0.3555, Train Acc: 0.4582, Train F1 Macro: 0.3624, Train F1 Micro: 0.3838, Val Loss: 0.3697, Val Acc: 0.4470, Val F1 Macro: 0.4275, Val F1 Micro: 0.4398\n", "Epoch [4/15], Time: 3.18s, Train Loss: 0.3397, Train Acc: 0.4760, Train F1 Macro: 0.3924, Train F1 Micro: 0.4068, Val Loss: 0.4214, Val Acc: 0.4198, Val F1 Macro: 0.4303, Val F1 Micro: 0.4348\n", "Epoch [5/15], Time: 3.16s, Train Loss: 0.3269, Train Acc: 0.4999, Train F1 Macro: 0.4157, Train F1 Micro: 0.4254, Val Loss: 0.4273, Val Acc: 0.4206, Val F1 Macro: 0.4300, Val F1 Micro: 0.4319\n", "Epoch [6/15], Time: 3.19s, Train Loss: 0.3128, Train Acc: 0.5306, Train F1 Macro: 0.4363, Train F1 Micro: 0.4430, Val Loss: 0.4973, Val Acc: 0.3964, Val F1 Macro: 0.4268, Val F1 Micro: 0.4260\n", "Epoch [7/15], Time: 3.20s, Train Loss: 0.2986, Train Acc: 0.5549, Train F1 Macro: 0.4544, Train F1 Micro: 0.4590, Val Loss: 0.6225, Val Acc: 0.3668, Val F1 Macro: 0.4198, Val F1 Micro: 0.4176\n", "Epoch [8/15], Time: 3.23s, Train Loss: 0.2829, Train Acc: 0.5990, Train F1 Macro: 0.4733, Train F1 Micro: 0.4765, Val Loss: 0.7199, Val Acc: 0.3411, Val F1 Macro: 0.4108, Val F1 Micro: 0.4080\n", "Epoch [9/15], Time: 3.22s, Train Loss: 0.2640, Train Acc: 0.6329, Train F1 Macro: 0.4915, Train F1 Micro: 0.4938, Val Loss: 0.9489, Val Acc: 0.3154, Val F1 Macro: 0.4003, Val F1 Micro: 0.3977\n", "Epoch [10/15], Time: 3.26s, Train Loss: 0.2419, Train Acc: 0.6781, Train F1 Macro: 0.5107, Train F1 Micro: 0.5123, Val Loss: 1.1723, Val Acc: 0.2983, Val F1 Macro: 0.3900, Val F1 Micro: 0.3878\n", "Epoch [11/15], Time: 3.24s, Train Loss: 0.2200, Train Acc: 0.7192, Train F1 Macro: 0.5301, Train F1 Micro: 0.5311, Val Loss: 1.3870, Val Acc: 0.3100, Val F1 Macro: 0.3822, Val F1 Micro: 0.3807\n", "Epoch [12/15], Time: 3.30s, Train Loss: 0.1960, Train Acc: 0.7665, Train F1 Macro: 0.5500, Train F1 Micro: 0.5507, Val Loss: 1.7270, Val Acc: 0.2913, Val F1 Macro: 0.3739, Val F1 Micro: 0.3732\n", "Epoch [13/15], Time: 3.34s, Train Loss: 0.1744, Train Acc: 0.7982, Train F1 Macro: 0.5692, Train F1 Micro: 0.5697, Val Loss: 1.9118, Val Acc: 0.3131, Val F1 Macro: 0.3679, Val F1 Micro: 0.3686\n", "Epoch [14/15], Time: 3.28s, Train Loss: 0.1531, Train Acc: 0.8294, Train F1 Macro: 0.5880, Train F1 Micro: 0.5883, Val Loss: 2.6356, Val Acc: 0.2944, Val F1 Macro: 0.3616, Val F1 Micro: 0.3633\n", "Epoch [15/15], Time: 3.32s, Train Loss: 0.1347, Train Acc: 0.8525, Train F1 Macro: 0.6059, Train F1 Micro: 0.6059, Val Loss: 2.7434, Val Acc: 0.3022, Val F1 Macro: 0.3571, Val F1 Micro: 0.3592\n", "Total Training Time: 48.40s\n", "Test Loss: 0.3652, Test Acc: 0.4301, Test F1 Macro: 0.4249, Test F1 Micro: 0.4301\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\TEXTGCN\\lib\\site-packages\\torch\\nn\\modules\\rnn.py:82: UserWarning: dropout option adds dropout after all but last recurrent layer, so non-zero dropout expects num_layers greater than 1, but got dropout=0.5 and num_layers=1\n", "  warnings.warn(\"dropout option adds dropout after all but last \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["***** Best Result Updated at Epoch 1, Val Loss: 0.3876 *****\n", "Epoch [1/20], Time: 3.22s, Train Loss: 0.4383, Train Acc: 0.2910, Train F1 Macro: 0.2151, Train F1 Micro: 0.2910, Val Loss: 0.3876, Val Acc: 0.4245, Val F1 Macro: 0.3471, Val F1 Micro: 0.4245\n", "***** Best Result Updated at Epoch 2, Val Loss: 0.3659 *****\n", "Epoch [2/20], Time: 3.20s, Train Loss: 0.3796, Train Acc: 0.4226, Train F1 Macro: 0.3059, Train F1 Micro: 0.3568, Val Loss: 0.3659, Val Acc: 0.4525, Val F1 Macro: 0.4001, Val F1 Micro: 0.4385\n", "Epoch [3/20], Time: 3.18s, Train Loss: 0.3610, Train Acc: 0.4466, Train F1 Macro: 0.3572, Train F1 Micro: 0.3867, Val Loss: 0.3819, Val Acc: 0.4276, Val F1 Macro: 0.4115, Val F1 Micro: 0.4348\n", "Epoch [4/20], Time: 3.20s, Train Loss: 0.3489, Train Acc: 0.4621, Train F1 Macro: 0.3866, Train F1 Micro: 0.4056, Val Loss: 0.4209, Val Acc: 0.4073, Val F1 Macro: 0.4087, Val F1 Micro: 0.4280\n", "Epoch [5/20], Time: 3.15s, Train Loss: 0.3371, Train Acc: 0.4808, Train F1 Macro: 0.4078, Train F1 Micro: 0.4206, Val Loss: 0.4717, Val Acc: 0.4182, Val F1 Macro: 0.4067, Val F1 Micro: 0.4260\n", "Epoch [6/20], Time: 3.22s, Train Loss: 0.3192, Train Acc: 0.5188, Train F1 Macro: 0.4285, Train F1 Micro: 0.4370, Val Loss: 0.6556, Val Acc: 0.3653, Val F1 Macro: 0.3953, Val F1 Micro: 0.4159\n", "Epoch [7/20], Time: 3.21s, Train Loss: 0.2989, Train Acc: 0.5666, Train F1 Macro: 0.4507, Train F1 Micro: 0.4555, Val Loss: 0.8347, Val Acc: 0.3832, Val F1 Macro: 0.3885, Val F1 Micro: 0.4112\n", "Epoch [8/20], Time: 3.22s, Train Loss: 0.2760, Train Acc: 0.6107, Train F1 Macro: 0.4734, Train F1 Micro: 0.4749, Val Loss: 0.8821, Val Acc: 0.3629, Val F1 Macro: 0.3828, Val F1 Micro: 0.4052\n", "Epoch [9/20], Time: 3.23s, Train Loss: 0.2535, Train Acc: 0.6583, Train F1 Macro: 0.4962, Train F1 Micro: 0.4953, Val Loss: 1.0666, Val Acc: 0.3621, Val F1 Macro: 0.3779, Val F1 Micro: 0.4004\n", "Epoch [10/20], Time: 3.25s, Train Loss: 0.2298, Train Acc: 0.7019, Train F1 Macro: 0.5191, Train F1 Micro: 0.5159, Val Loss: 1.2926, Val Acc: 0.3497, Val F1 Macro: 0.3724, Val F1 Micro: 0.3953\n", "Epoch [11/20], Time: 3.26s, Train Loss: 0.2077, Train Acc: 0.7468, Train F1 Macro: 0.5417, Train F1 Micro: 0.5369, Val Loss: 1.4336, Val Acc: 0.3458, Val F1 Macro: 0.3674, Val F1 Micro: 0.3908\n", "Epoch [12/20], Time: 3.30s, Train Loss: 0.1866, Train Acc: 0.7850, Train F1 Macro: 0.5635, Train F1 Micro: 0.5576, Val Loss: 1.6974, Val Acc: 0.3474, Val F1 Macro: 0.3634, Val F1 Micro: 0.3872\n", "Epoch [13/20], Time: 3.30s, Train Loss: 0.1675, Train Acc: 0.8128, Train F1 Macro: 0.5839, Train F1 Micro: 0.5772, Val Loss: 2.0688, Val Acc: 0.3263, Val F1 Macro: 0.3586, Val F1 Micro: 0.3825\n", "Epoch [14/20], Time: 3.32s, Train Loss: 0.1507, Train Acc: 0.8468, Train F1 Macro: 0.6037, Train F1 Micro: 0.5965, Val Loss: 2.3537, Val Acc: 0.3154, Val F1 Macro: 0.3539, Val F1 Micro: 0.3777\n", "Epoch [15/20], Time: 3.31s, Train Loss: 0.1337, Train Acc: 0.8701, Train F1 Macro: 0.6223, Train F1 Micro: 0.6147, Val Loss: 2.5935, Val Acc: 0.3271, Val F1 Macro: 0.3503, Val F1 Micro: 0.3744\n", "Epoch [16/20], Time: 3.38s, Train Loss: 0.1181, Train Acc: 0.8919, Train F1 Macro: 0.6398, Train F1 Micro: 0.6320, Val Loss: 3.0114, Val Acc: 0.3170, Val F1 Macro: 0.3467, Val F1 Micro: 0.3708\n", "Epoch [17/20], Time: 3.35s, Train Loss: 0.1035, Train Acc: 0.9088, Train F1 Macro: 0.6562, Train F1 Micro: 0.6483, Val Loss: 3.2507, Val Acc: 0.3162, Val F1 Macro: 0.3435, Val F1 Micro: 0.3676\n", "Epoch [18/20], Time: 3.35s, Train Loss: 0.0913, Train Acc: 0.9234, Train F1 Macro: 0.6715, Train F1 Micro: 0.6636, Val Loss: 3.4200, Val Acc: 0.3045, Val F1 Macro: 0.3400, Val F1 Micro: 0.3641\n", "Epoch [19/20], Time: 3.38s, Train Loss: 0.0797, Train Acc: 0.9371, Train F1 Macro: 0.6858, Train F1 Micro: 0.6780, Val Loss: 4.1778, Val Acc: 0.2991, Val F1 Macro: 0.3367, Val F1 Micro: 0.3606\n", "Epoch [20/20], Time: 3.35s, Train Loss: 0.0719, Train Acc: 0.9464, Train F1 Macro: 0.6991, Train F1 Micro: 0.6914, Val Loss: 4.3373, Val Acc: 0.3022, Val F1 Macro: 0.3337, Val F1 Micro: 0.3577\n", "Total Training Time: 65.40s\n", "Test Loss: 0.3710, Test Acc: 0.4246, Test F1 Macro: 0.4085, Test F1 Micro: 0.4246\n"]}], "source": ["for num_epochs in [5,10,15,20]:\n", "    vocab_size = 30522\n", "    embedding_dim = 128\n", "    n_filters = 128\n", "    filter_sizes = [3,4,5]\n", "    output_dim = 6\n", "    dropout = 0.5\n", "    padding_idx = 0\n", "    input_dim = 6 * metadata_each_dim\n", "    input_dim_metadata = 5\n", "    hidden_dim = 64\n", "    n_layers = 1\n", "    bidirectional = True\n", "\n", "    model = LiarModel(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional).to(DEVICE)\n", "\n", "\n", "    # Define the optimizer and loss function\n", "    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)\n", "    criterion = nn.BCEWithLogitsLoss()\n", "\n", "\n", "    # Record the training process\n", "    Train_acc = []\n", "    Train_loss = []\n", "    Train_macro_f1 = []\n", "    Train_micro_f1 = []\n", "\n", "    Val_acc = []\n", "    Val_loss = []\n", "    Val_macro_f1 = []\n", "    Val_micro_f1 = []\n", "\n", "    def train(num_epochs, model, train_loader, val_loader, optimizer, criterion, model_save):\n", "        epoch_trained = 0\n", "        train_label_all = []\n", "        train_predict_all = []\n", "        val_label_all = []\n", "        val_predict_all = []\n", "        best_valid_loss = float('inf')\n", "\n", "        start_time = time.time()\n", "        for epoch in range(num_epochs):\n", "            epoch_trained += 1\n", "            epoch_start_time = time.time()\n", "            # Training\n", "            model.train()\n", "            train_loss = 0.0\n", "            train_accuracy = 0.0\n", "            for statements, label_onehot, label, metadata_text, metadata_number in train_loader:\n", "                statements = statements.to(DEVICE)\n", "                label_onehot = label_onehot.to(DEVICE)\n", "                label = label.to(DEVICE)\n", "                metadata_text = metadata_text.to(DEVICE)\n", "                metadata_number = metadata_number.to(DEVICE)\n", "\n", "                optimizer.zero_grad()\n", "                outputs = model(statements, metadata_text, metadata_number)\n", "                loss = criterion(outputs, label_onehot)\n", "                loss.backward()\n", "                optimizer.step()\n", "\n", "                train_loss += loss.item()\n", "                _, train_predicted = torch.max(outputs, 1)\n", "                train_accuracy += sum(train_predicted == label)\n", "                train_predict_all += train_predicted.tolist()\n", "                train_label_all += label.tolist()\n", "            train_loss /= len(train_loader)\n", "            train_accuracy /= len(train_loader.dataset)\n", "            train_macro_f1 = f1_score(train_label_all, train_predict_all, average='macro')\n", "            train_micro_f1 = f1_score(train_label_all, train_predict_all, average='micro')\n", "\n", "            Train_acc.append(train_accuracy.tolist())\n", "            Train_loss.append(train_loss)\n", "            Train_macro_f1.append(train_macro_f1)\n", "            Train_micro_f1.append(train_micro_f1)\n", "\n", "            # Validation\n", "            model.eval()\n", "            val_loss = 0.0\n", "            val_accuracy = 0.0\n", "            with torch.no_grad():\n", "                for statements, label_onehot, label, metadata_text, metadata_number in val_loader:\n", "                    statements = statements.to(DEVICE)\n", "                    label_onehot = label_onehot.to(DEVICE)\n", "                    label = label.to(DEVICE)\n", "                    metadata_text = metadata_text.to(DEVICE)\n", "                    metadata_number = metadata_number.to(DEVICE)\n", "\n", "                    val_outputs = model(statements, metadata_text, metadata_number)\n", "                    val_loss += criterion(val_outputs, label_onehot).item()\n", "                    _, val_predicted = torch.max(val_outputs, 1)\n", "                    val_accuracy += sum(val_predicted == label)\n", "                    val_predict_all += val_predicted.tolist()\n", "                    val_label_all += label.tolist()\n", "            val_loss /= len(val_loader)\n", "            val_accuracy /= len(val_loader.dataset)\n", "            val_macro_f1 = f1_score(val_label_all, val_predict_all, average='macro')\n", "            val_micro_f1 = f1_score(val_label_all, val_predict_all, average='micro')\n", "\n", "            Val_acc.append(val_accuracy.tolist())\n", "            Val_loss.append(val_loss)\n", "            Val_macro_f1.append(val_macro_f1)\n", "            Val_micro_f1.append(val_micro_f1)\n", "\n", "            if val_loss < best_valid_loss:\n", "                best_valid_loss = val_loss\n", "                torch.save(model.state_dict(), model_save)\n", "                print(f'***** Best Result Updated at Epoch {epoch_trained}, Val Loss: {val_loss:.4f} *****')\n", "\n", "            # Print the losses and accuracy\n", "            epoch_end_time = time.time()\n", "            epoch_time = epoch_end_time - epoch_start_time\n", "\n", "            print(f\"Epoch [{epoch+1}/{num_epochs}], Time: {epoch_time:.2f}s, Train Loss: {train_loss:.4f}, Train Acc: {train_accuracy:.4f}, Train F1 Macro: {train_macro_f1:.4f}, Train F1 Micro: {train_micro_f1:.4f}, Val Loss: {val_loss:.4f}, Val Acc: {val_accuracy:.4f}, Val F1 Macro: {val_macro_f1:.4f}, Val F1 Micro: {val_micro_f1:.4f}\")\n", "\n", "        end_time = time.time()\n", "        training_time = end_time - start_time\n", "        print(f'Total Training Time: {training_time:.2f}s')\n", "\n", "\n", "    train(num_epochs, model, train_loader, val_loader, optimizer, criterion, model_save)\n", "\n", "\n", "    # Evaluate the model on new data\n", "    def test(model, test_loader, model_save):\n", "        model.load_state_dict(torch.load(model_save))\n", "        model.eval()\n", "\n", "        test_label_all = []\n", "        test_predict_all = []\n", "\n", "        test_loss = 0.0\n", "        test_accuracy = 0.0\n", "        with torch.no_grad():\n", "            for statements, label_onehot, label, metadata_text, metadata_number in test_loader:\n", "                statements = statements.to(DEVICE)\n", "                label_onehot = label_onehot.to(DEVICE)\n", "                label = label.to(DEVICE)\n", "                metadata_text = metadata_text.to(DEVICE)\n", "                metadata_number = metadata_number.to(DEVICE)\n", "\n", "                test_outputs = model(statements, metadata_text, metadata_number)\n", "                test_loss += criterion(test_outputs, label_onehot).item()\n", "                _, test_predicted = torch.max(test_outputs, 1)\n", "                \n", "                test_accuracy += sum(test_predicted == label)\n", "                test_predict_all += test_predicted.tolist()\n", "                test_label_all += label.tolist()\n", "\n", "        test_loss /= len(test_loader)\n", "        test_accuracy /= len(test_loader.dataset)\n", "        test_macro_f1 = f1_score(test_label_all, test_predict_all, average='macro')\n", "        test_micro_f1 = f1_score(test_label_all, test_predict_all, average='micro')\n", "\n", "        print(f'Test Loss: {test_loss:.4f}, Test Acc: {test_accuracy:.4f}, Test F1 Macro: {test_macro_f1:.4f}, Test F1 Micro: {test_micro_f1:.4f}')\n", "\n", "\n", "    test(model, test_loader, model_save)"]}, {"cell_type": "code", "execution_count": 226, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "TEXTGCN", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}