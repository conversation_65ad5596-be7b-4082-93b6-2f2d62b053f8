# ===============================
# Title: Fuzzy Deep Hybrid Network (FDHN) for Fake News Detection
# Author: <PERSON>
# Create Date: Apr 21, 2023
# Revision Date: Otc 23, 2023
# Github: https://github.com/chengxuphd/FDHN
# DOI: https://doi.org/10.1145/3628797.3628971
# Description: This is the source code of FDHN model. The dataset used in this code is LIAR (https://www.cs.ucsb.edu/~william/data/liar_dataset.zip).
# Notes: This is code for the TC+TC+CB+FZ experiment.
# ===============================

import pandas as pd
import numpy as np
import time
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.utils.data as data
from sklearn.metrics import f1_score
from torch.utils.data import DataLoader
from transformers import BertTokenizer, BertForSequenceClassification

# Fixing the randomness of CUDA.
torch.backends.cudnn.deterministic = True
torch.backends.cudnn.benchmark = False

np.random.seed(42)
torch.manual_seed(42)

DEVICE = torch.device("cuda") if torch.cuda.is_available() else torch.device("cpu")
print("PyTorch Version : {}".format(torch.__version__))
print(DEVICE)


worksapce = 'C:/Users/<USER>/Desktop/HLAN/FAKE/liar_dataset/'
model_save = 'TC+TC+CB+FZ.pt'
model_name = 'TC+TC+CB+FZ'
num_epochs = 10
batch_size = 32
learning_rate = 1e-3
num_classes = 6
padding_idx = 0
metadata_each_dim = 10


col = ['id', 'label', 'statement', 'subject', 'speaker', 'job_title', 'state_info', 'party_affiliation', 'barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts', 'context']

label_map = {0: 'pants-fire', 1: 'false', 2: 'barely-true', 3: 'half-true', 4: 'mostly-true', 5: 'true'}
label_convert = {'pants-fire': 0, 'false': 1, 'barely-true': 2, 'half-true': 3, 'mostly-true': 4, 'true':5}

train_data = pd.read_csv(worksapce + 'train.tsv', sep = '\t', names = col)
test_data = pd.read_csv(worksapce + 'test.tsv', sep = '\t', names = col)
val_data = pd.read_csv(worksapce + 'valid.tsv', sep = '\t', names = col)

# Replace NaN values with 'NaN'
train_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = train_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)
train_data.fillna('NaN', inplace=True)

test_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = test_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)
test_data.fillna('NaN', inplace=True)

val_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']] = val_data[['barely_true_counts', 'false_counts', 'half_true_counts', 'mostly_true_counts', 'pants_on_fire_counts']].fillna(0)
val_data.fillna('NaN', inplace=True)




def textProcess(input_text, max_length = -1):
    tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
    if max_length == -1:
        tokens = tokenizer(input_text, truncation=True, padding=True)
    else:
        tokens = tokenizer(input_text, truncation=True, padding='max_length', max_length=max_length)
    return tokens



# Define a custom dataset for loading the data
class LiarDataset(data.Dataset):
    def __init__(self, data_df, statement, label_onehot, label, subject, speaker, job_title, state_info,
                     party_affiliation, barely_true_counts, false_counts, half_true_counts, mostly_true_counts,
                    pants_on_fire_counts, context):
        self.data_df = data_df
        self.statement = statement
        self.label_onehot = label_onehot
        self.label = label
        self.metadata_text = torch.cat((subject.int(), speaker.int(), job_title.int(), state_info.int(), party_affiliation.int(), 
                                   context.int()), dim=-1)
        self.metadata_number = torch.cat((torch.tensor(barely_true_counts, dtype=torch.float).unsqueeze(1), torch.tensor(false_counts, dtype=torch.float).unsqueeze(1), 
                                   torch.tensor(half_true_counts, dtype=torch.float).unsqueeze(1), torch.tensor(mostly_true_counts, dtype=torch.float).unsqueeze(1), 
                                   torch.tensor(pants_on_fire_counts, dtype=torch.float).unsqueeze(1)), dim=-1)


        
    def __len__(self):
        return len(self.data_df)
    
    def __getitem__(self, idx):
        statement = self.statement[idx]
        label_onehot = self.label_onehot[idx]
        label = self.label[idx]
        metadata_text = self.metadata_text[idx]
        metadata_number = self.metadata_number[idx]
        return statement, label_onehot, label, metadata_text, metadata_number


# Define the data loaders for training and validation
train_text = torch.tensor(textProcess(train_data['statement'].tolist())['input_ids'])
train_label = torch.nn.functional.one_hot(torch.tensor(train_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)
train_subject = torch.tensor(textProcess(train_data['subject'].tolist(), metadata_each_dim)['input_ids'])
train_speaker = torch.tensor(textProcess(train_data['speaker'].tolist(), metadata_each_dim)['input_ids'])
train_job_title = torch.tensor(textProcess(train_data['job_title'].tolist(), metadata_each_dim)['input_ids'])
train_state_info = torch.tensor(textProcess(train_data['state_info'].tolist(), metadata_each_dim)['input_ids'])
train_party_affiliation = torch.tensor(textProcess(train_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])
train_context = torch.tensor(textProcess(train_data['context'].tolist(), metadata_each_dim)['input_ids'])

train_dataset = LiarDataset(train_data, train_text, train_label, torch.tensor(train_data['label'].replace(label_convert)), 
                            train_subject, train_speaker, train_job_title, 
                            train_state_info, train_party_affiliation, 
                            train_data['barely_true_counts'].tolist(), train_data['false_counts'].tolist(), 
                            train_data['half_true_counts'].tolist(), train_data['mostly_true_counts'].tolist(), 
                            train_data['pants_on_fire_counts'].tolist(), train_context)
train_loader = data.DataLoader(train_dataset, batch_size=batch_size, shuffle=True)

val_text = torch.tensor(textProcess(val_data['statement'].tolist())['input_ids'])
val_label = torch.nn.functional.one_hot(torch.tensor(val_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)
val_subject = torch.tensor(textProcess(val_data['subject'].tolist(), metadata_each_dim)['input_ids'])
val_speaker = torch.tensor(textProcess(val_data['speaker'].tolist(), metadata_each_dim)['input_ids'])
val_job_title = torch.tensor(textProcess(val_data['job_title'].tolist(), metadata_each_dim)['input_ids'])
val_state_info = torch.tensor(textProcess(val_data['state_info'].tolist(), metadata_each_dim)['input_ids'])
val_party_affiliation = torch.tensor(textProcess(val_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])
val_context = torch.tensor(textProcess(val_data['context'].tolist(), metadata_each_dim)['input_ids'])

val_dataset = LiarDataset(val_data, val_text, val_label, torch.tensor(val_data['label'].replace(label_convert)),
                          val_subject, val_speaker, val_job_title, 
                          val_state_info, val_party_affiliation, 
                          val_data['barely_true_counts'].tolist(), val_data['false_counts'].tolist(), 
                          val_data['half_true_counts'].tolist(), val_data['mostly_true_counts'].tolist(), 
                          val_data['pants_on_fire_counts'].tolist(), val_context)
val_loader = data.DataLoader(val_dataset, batch_size=batch_size)

test_text = torch.tensor(textProcess(test_data['statement'].tolist())['input_ids'])
test_label = torch.nn.functional.one_hot(torch.tensor(test_data['label'].replace(label_convert)), num_classes=6).type(torch.float64)
test_subject = torch.tensor(textProcess(test_data['subject'].tolist(), metadata_each_dim)['input_ids'])
test_speaker = torch.tensor(textProcess(test_data['speaker'].tolist(), metadata_each_dim)['input_ids'])
test_job_title = torch.tensor(textProcess(test_data['job_title'].tolist(), metadata_each_dim)['input_ids'])
test_state_info = torch.tensor(textProcess(test_data['state_info'].tolist(), metadata_each_dim)['input_ids'])
test_party_affiliation = torch.tensor(textProcess(test_data['party_affiliation'].tolist(), metadata_each_dim)['input_ids'])
test_context = torch.tensor(textProcess(test_data['context'].tolist(), metadata_each_dim)['input_ids'])

test_dataset = LiarDataset(test_data, test_text, test_label, torch.tensor(test_data['label'].replace(label_convert)),
                          test_subject, test_speaker, test_job_title, 
                          test_state_info, test_party_affiliation, 
                          test_data['barely_true_counts'].tolist(), test_data['false_counts'].tolist(), 
                          test_data['half_true_counts'].tolist(), test_data['mostly_true_counts'].tolist(), 
                          test_data['pants_on_fire_counts'].tolist(), test_context)
test_loader = data.DataLoader(test_dataset, batch_size=batch_size)




import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.autograd import Variable


def squash(inputs, axis=-1):
    norm = torch.norm(inputs, p=2, dim=axis, keepdim=True)
    scale = norm**2 / (1 + norm**2) / (norm + 1e-8)
    return scale * inputs


class DenseCapsule(nn.Module):
    def __init__(self, in_num_caps, in_dim_caps, out_num_caps, out_dim_caps, routings=3):
        super(DenseCapsule, self).__init__()
        self.in_num_caps = in_num_caps
        self.in_dim_caps = in_dim_caps
        self.out_num_caps = out_num_caps
        self.out_dim_caps = out_dim_caps
        self.routings = routings
        self.weight = nn.Parameter(0.01 * torch.randn(out_num_caps, in_num_caps, out_dim_caps, in_dim_caps))

    def forward(self, x):
        # x.size=[batch, in_num_caps, in_dim_caps]
        # expanded to    [batch, 1,            in_num_caps, in_dim_caps,  1]
        # weight.size   =[       out_num_caps, in_num_caps, out_dim_caps, in_dim_caps]
        # torch.matmul: [out_dim_caps, in_dim_caps] x [in_dim_caps, 1] -> [out_dim_caps, 1]
        # => x_hat.size =[batch, out_num_caps, in_num_caps, out_dim_caps]
        x_hat = torch.squeeze(torch.matmul(self.weight, x[:, None, :, :, None]), dim=-1)

        # In forward pass, `x_hat_detached` = `x_hat`;
        # In backward, no gradient can flow from `x_hat_detached` back to `x_hat`.
        x_hat_detached = x_hat.detach()

        # The prior for coupling coefficient, initialized as zeros.
        # b.size = [batch, out_num_caps, in_num_caps]
        b = Variable(torch.zeros(x.size(0), self.out_num_caps, self.in_num_caps)).cuda()

        assert self.routings > 0, 'The \'routings\' should be > 0.'
        for i in range(self.routings):
            # c.size = [batch, out_num_caps, in_num_caps]
            c = F.softmax(b, dim=1)

            # At last iteration, use `x_hat` to compute `outputs` in order to backpropagate gradient
            if i == self.routings - 1:
                # c.size expanded to [batch, out_num_caps, in_num_caps, 1           ]
                # x_hat.size     =   [batch, out_num_caps, in_num_caps, out_dim_caps]
                # => outputs.size=   [batch, out_num_caps, 1,           out_dim_caps]
                outputs = squash(torch.sum(c[:, :, :, None] * x_hat, dim=-2, keepdim=True))
                # outputs = squash(torch.matmul(c[:, :, None, :], x_hat))  # alternative way
            else:  # Otherwise, use `x_hat_detached` to update `b`. No gradients flow on this path.
                outputs = squash(torch.sum(c[:, :, :, None] * x_hat_detached, dim=-2, keepdim=True))
                # outputs = squash(torch.matmul(c[:, :, None, :], x_hat_detached))  # alternative way

                # outputs.size       =[batch, out_num_caps, 1,           out_dim_caps]
                # x_hat_detached.size=[batch, out_num_caps, in_num_caps, out_dim_caps]
                # => b.size          =[batch, out_num_caps, in_num_caps]
                b = b + torch.sum(outputs * x_hat_detached, dim=-1)

        return torch.squeeze(outputs, dim=-2)


class PrimaryCapsule(nn.Module):
    def __init__(self, in_channels, out_channels, dim_caps, kernel_size, stride=1, padding=0):
        super(PrimaryCapsule, self).__init__()
        self.dim_caps = dim_caps
        self.conv2d = nn.Conv2d(in_channels, out_channels, kernel_size=kernel_size, stride=stride, padding=padding)

    def forward(self, x):
        outputs = self.conv2d(x)
        # print(outputs.shape)
        outputs = outputs.view(x.size(0), -1, self.dim_caps)
        return squash(outputs)

class CapsuleNet2(nn.Module):
    def __init__(self, input_size, classes, routings):
        super(CapsuleNet2, self).__init__()
        self.input_size = input_size
        self.classes = classes
        self.routings = routings

        # Layer 1: Just a conventional Conv2D layer
        self.conv1 = nn.Conv2d(3, 1, kernel_size=2, stride=1, padding=0)
        
        # Layer 2: Conv2D layer with `squash` activation, then reshape to [None, num_caps, dim_caps]
        self.primarycaps = PrimaryCapsule(1, 1, 18, kernel_size=5, stride=2, padding=0)

        # Layer 3: Capsule layer. Routing algorithm works here.
        self.digitcaps = DenseCapsule(in_num_caps=93, in_dim_caps=18,
                                      out_num_caps=classes, out_dim_caps=100, routings=routings)


        self.relu = nn.ReLU()

    def forward(self, x, y=None):
        x = self.relu(self.conv1(x))
        # print("x1.shape: ",x.shape) #[128, 64, 48, 98])  64和第一层有关 #[32, 1, 57, 127])
        x = self.primarycaps(x) #torch.Size([128, 60, 8])
        # print("x2.shape: ",x.shape) #
        x = self.digitcaps(x) #[128, 16, 5])
        # print("x3.shape: ",x.shape)
        return x

def caps_loss(y_true, y_pred, x, x_recon, lam_recon):
    L = y_true * torch.clamp(0.9 - y_pred, min=0.) ** 2 + \
        0.5 * (1 - y_true) * torch.clamp(y_pred - 0.1, min=0.) ** 2
    L_margin = L.sum(dim=1).mean()

    L_recon = nn.MSELoss()(x_recon, x)

    return L_margin + lam_recon * L_recon

class capnet2(nn.Module):
    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):
        super().__init__()

        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)
        self.convs = nn.ModuleList([
                                    nn.Conv1d(in_channels = embedding_dim, 
                                              out_channels = n_filters, 
                                              kernel_size = fs)
                                    for fs in [3,3,3]
                                    ])   
        
        self.cap = CapsuleNet2(([ 3, 58, 128]), classes=6, routings=3)
        
    def forward(self, text):
        #text = [batch size, sent len]
        embedded = self.embedding(text)
        #embedded = [batch size, sent len, emb dim]

        embedded = embedded.permute(0, 2, 1)
        #embedded = [batch size, emb dim, sent len]                         # torch.Size([32, 128, 58])    # torch.Size([32, 128, 57])    # torch.Size([32, 128, 56])
#         

        conved = [F.relu(conv(embedded)) for conv in self.convs]
        tacked_tensor = torch.stack([conved[0], conved[1], conved[2]], dim=3)
        tacked_tensor = tacked_tensor.permute(0, 3, 2, 1) # [batch size, dim, sent len, embed_dim]   ([32, 3, 58, 128])
        tacked_tensor = self.cap(tacked_tensor)           #[32, 6, 100]
        tacked_tensor = F.max_pool1d(tacked_tensor, tacked_tensor.shape[2]).squeeze()
        # print(tacked_tensor.shape)
        return tacked_tensor


import torch
from torch import nn
from torch.nn import Embedding
import torch.nn.functional as F

class CoAttention(nn.Module):
    def __init__(self,hidde_size:int,attention_size:int): #hidden_size:d, attention_size:k
        super(CoAttention, self).__init__()

        self.hidden_size=hidde_size
        self.Wl=nn.Parameter(torch.zeros(size=(hidde_size*2,hidde_size*2)),requires_grad=True)
        self.Ws=nn.Parameter(torch.zeros(size=(attention_size,hidde_size*2)),requires_grad=True)
        self.Wc=nn.Parameter(torch.zeros(size=(attention_size,hidde_size*2)),requires_grad=True)
        self.whs=nn.Parameter(torch.zeros(size=(1,attention_size)),requires_grad=True)
        self.whc=nn.Parameter(torch.zeros(size=(1,attention_size)),requires_grad=True)
        self.reset_parameters()
        
    def reset_parameters(self):
        self.Wl.data.uniform_(-1.0,1.0)
        self.Ws.data.uniform_(-1.0,1.0)
        self.Wc.data.uniform_(-1.0,1.0)
        self.whs.data.uniform_(-1.0,1.0)
        self.whc.data.uniform_(-1.0,1.0)

    def forward(self,new_batch,entity_desc_batch):
        # news_batch: [batch size, N, hidden size *2] hidden size:h
        S=torch.transpose(new_batch,1,2)
        # entity_desc_batch: [batch size, T, hidden size *2] T: entity description sentences for a news
        C=torch.transpose(entity_desc_batch,1,2)

        attF=torch.tanh(torch.bmm(torch.transpose(C,1,2),torch.matmul(self.Wl,S))) #dim [batch_size,T,N]

        WsS=torch.matmul(self.Ws,S) #dim[batch,a,N] a:attention size
        WsC=torch.matmul(self.Wc,C) #dim[batch,a,T]

        Hs=torch.tanh(WsS+torch.bmm(WsC,attF)) #dim[batch,a,N]
        Hc=torch.tanh(WsC+torch.bmm(WsS,torch.transpose(attF,1,2))) #dim[batch,a,T]


        a_s=F.softmax(torch.matmul(self.whs,Hs),dim=2) #dim[batch,1,N]
        a_c=F.softmax(torch.matmul(self.whc,Hc),dim=2) #dim[batch,1,T]

        s=torch.bmm(a_s,new_batch) # dim[batch,1,2h]
        c=torch.bmm(a_c,entity_desc_batch)#[batch,1,2h]
        return s,c,a_s,a_c
    
class Classifier(nn.Module):
    def __init__(self,hidden_size:int):
        super(Classifier, self).__init__()
        # self.l1=nn.Linear(hidden_size*,hidden_size*4) # news sent+kb sent concatenation
        self.l2=nn.Linear(hidden_size*4,6) 

    def forward(self,news_batch):
        # layer1=self.l1(news_batch)
        ans=self.l2(news_batch)
        y=torch.squeeze(ans,1)
        #y=F.softmax(ans,dim=1)
        return y

class TextTextual(nn.Module):
    def __init__(self, vocab_size, embedding_dim, input_dim,n_filters, filter_sizes, output_dim, dropout, pad_idx):
        super().__init__()
        hidden_size = 16
        num_layers = 2
        self.hidden_dim = 16
        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)
        self.embedding1 = nn.Embedding(vocab_size, input_dim, padding_idx = pad_idx)
        self.gru=nn.GRU(input_size=embedding_dim,hidden_size=hidden_size,num_layers=num_layers,batch_first=True,dropout=dropout,bidirectional=True)
        self.gru1=nn.GRU(input_size=input_dim,hidden_size=hidden_size,num_layers=num_layers,batch_first=True,dropout=dropout,bidirectional=True)
        self.att = CoAttention(self.hidden_dim,attention_size=5)
        self.dropout = nn.Dropout(dropout)
        self.classifier=Classifier(self.hidden_dim)

            
    
    def forward(self, text, textual):
        #text = [batch size, sent len]
        text_embedded = self.embedding(text)
        #embedded = [batch size, sent len, emb dim]
        tu_embedded = self.embedding1(textual)   #torch.Size([32, 512, 128]) torch.Size([32, 60, 128])
        text_embedded,_ = self.gru(text_embedded)
        tu_embedded,_ = self.gru1(tu_embedded)
        # print(text_embedded.shape, tu_embedded.shape)
        contents_att, desc_att, contents_cd_att_weight,desc_att_weight = self.att(text_embedded, tu_embedded)
        content_desc=torch.cat((contents_att,desc_att),2)
        content_desc=F.softmax(content_desc,dim=2) #dim[batch,1,4h]
        y=self.classifier(content_desc)


        return y


class FuzzyLayer1(nn.Module):  #广义钟形隶属函数
    def __init__(self, input_dim, membership_num):
        super(FuzzyLayer1, self).__init__()

        # input_dim: feature number of the dataset
        # membership_num: number of membership function, also known as the class number

        self.input_dim = input_dim
        self.membership_num = membership_num

        self.membership_miu = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)
        self.membership_a = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)
        self.membership_b = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)

        nn.init.xavier_uniform_(self.membership_miu)
        nn.init.ones_(self.membership_a)
        nn.init.ones_(self.membership_b)

    def forward(self, input_seq):
        batch_size = input_seq.size()[0]
        input_seq_exp = input_seq.unsqueeze(1).expand(batch_size, self.membership_num, self.input_dim)
        membership_miu_exp = self.membership_miu.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)
        membership_a_exp = self.membership_a.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)
        membership_b_exp = self.membership_b.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)

        # fuzzy_membership = torch.mean(torch.exp((-1 / 2) * ((input_seq_exp - membership_miu_exp) / membership_sigma_exp) ** 2), dim=-1)
        # print(fuzzy_membership.shape)  #torch.Size([32, 6])
        fuzzy_membership = torch.mean(1/1+(torch.abs((input_seq_exp - membership_miu_exp)/membership_a_exp)) ** membership_b_exp, dim=-1)
        return fuzzy_membership

class FuzzyLayer(nn.Module):   #高斯

    def __init__(self, input_dim, membership_num):
        super(FuzzyLayer, self).__init__()
        
#         input_dim: feature number of the dataset
#         membership_num: number of membership function, also known as the class number
        
        self.input_dim = input_dim
        self.membership_num = membership_num
        
        self.membership_miu = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)
        self.membership_sigma = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)
        
        nn.init.xavier_uniform_(self.membership_miu)
        nn.init.ones_(self.membership_sigma)
    def forward(self, input_seq):
        batch_size = input_seq.size()[0]
        input_seq_exp = input_seq.unsqueeze(1).expand(batch_size, self.membership_num, self.input_dim)
        membership_miu_exp = self.membership_miu.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)
        membership_sigma_exp = self.membership_sigma.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)

        fuzzy_membership = torch.mean(torch.exp((-1 / 2) * ((input_seq_exp - membership_miu_exp) / membership_sigma_exp) ** 2), dim=-1)
        return fuzzy_membership



class TextCNN(nn.Module):
    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):
        super().__init__()

        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)
        self.convs = nn.ModuleList([
                                    nn.Conv1d(in_channels = embedding_dim, 
                                              out_channels = n_filters, 
                                              kernel_size = fs)
                                    for fs in filter_sizes
                                    ])      
        
        self.fc = nn.Linear(len(filter_sizes) * n_filters, output_dim)
        self.dropout = nn.Dropout(dropout)
        
            
    
    def forward(self, text):
        #text = [batch size, sent len]
        embedded = self.embedding(text)
        #embedded = [batch size, sent len, emb dim]

        embedded = embedded.permute(0, 2, 1)
        #embedded = [batch size, emb dim, sent len]
#         print("embedded:",embedded.shape)
        conved = [F.relu(conv(embedded)) for conv in self.convs]
        #conved_n = [batch size, n_filters, sent len - filter_sizes[n] + 1]
#       
        
        pooled = [F.max_pool1d(conv, conv.shape[2]).squeeze(2) for conv in conved]
        #pooled_n = [batch size, n_filters]
        cat = self.dropout(torch.cat(pooled, dim = 1))
        #cat = [batch size, n_filters * len(filter_sizes)]

        return self.fc(cat)

class CNNBiLSTM(nn.Module):
    def __init__(self, input_dim, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout):
        super().__init__()

        self.embedding = nn.Linear(input_dim, embedding_dim)
        self.conv = nn.Conv1d(in_channels=embedding_dim, out_channels=32, kernel_size=1)
        self.rnn = nn.LSTM(32, 
                           hidden_dim, 
                           num_layers=n_layers, 
                           bidirectional=bidirectional, 
                           dropout=dropout)
        self.fc = nn.Linear(hidden_dim * 2, output_dim)
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, metadata):
        #metadata = [batch size, metadata dim]

        embedded = self.dropout(self.embedding(metadata))
        #embedded = [batch size, metadata dim, emb dim]

        embedded = torch.reshape(embedded, (metadata.size(0), 128, 1))

        conved = F.relu(self.conv(embedded))
        #conved = [batch size, n_filters, metadata dim - filter_sizes[n] + 1]
        conved = torch.reshape(conved, (metadata.size(0), 32))
        outputs, (hidden, cell) = self.rnn(conved)
        
        #outputs = [metadata dim - filter_sizes[n] + 1, batch size, hid dim * num directions]
        #hidden = [num layers * num directions, batch size, hid dim]
        #cell = [num layers * num directions, batch size, hid dim]

        #concat the final forward (hidden[-2,:,:]) and backward (hidden[-1,:,:]) hidden layers
        #and apply dropout
        # hidden = self.dropout(torch.cat((hidden[-1,:], hidden[0,:]), dim = -1))
        #hidden = [batch size, hid dim * num directions]

        return self.fc(outputs)


class LiarModel(nn.Module):
    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional):
        super().__init__()

        self.textcnn = TextCNN(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)
        self.textcnn2 = TextCNN(vocab_size, input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)
        self.cnn_bilstm = CNNBiLSTM(input_dim_metadata, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout)
        self.fuzzy = FuzzyLayer(output_dim, output_dim)
        self.fuse = nn.Linear(output_dim * 3, output_dim)
        # self.capNet1 = capnet1(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)
        self.capNet2 = capnet2(vocab_size, input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)
        self.fuzzy1 = FuzzyLayer1(output_dim, output_dim)
        self.TextTextual= TextTextual(vocab_size, embedding_dim,input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)
    def forward(self, text, metadata_text, metadata_number):
        #text = [batch size, sent len]
        #metadata = [batch size, metadata dim]

        
        # metadata_output_text = self.textcnn2(metadata_text)  #metadata_data[32,60]  metadata_output_text = torch.Size([32, 6])
        # text_output = self.capNet1(text)
        # text_output = self.textcnn(text)
        # metadata_output_text = self.capNet2(metadata_text)
        text_textual_output = self.TextTextual(text, metadata_text)
        # print(text_textual_output.shape)
        metadata_output_number = self.cnn_bilstm(metadata_number)
        metadata_output_fuzzy = self.fuzzy(metadata_output_number)

        fused_output = self.fuse(torch.cat((text_textual_output, metadata_output_number, metadata_output_fuzzy), dim=1))

        return fused_output





num_epochs =10  #3,5,7,10,15,20

for num_epochs in [3,5,7,10,15,20]:
    print("-----------------------------",num_epochs)
    vocab_size = 30522
    embedding_dim = 128
    n_filters = 128
    filter_sizes = [3,4,5]
    output_dim = 6
    dropout = 0.5
    padding_idx = 0
    input_dim = 6 * metadata_each_dim
    input_dim_metadata = 5
    hidden_dim = 64
    n_layers = 1
    bidirectional = True

    model = LiarModel(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional).to(DEVICE)


    # Define the optimizer and loss function
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
    criterion = nn.BCEWithLogitsLoss()


    # Record the training process
    Train_acc = []
    Train_loss = []
    Train_macro_f1 = []
    Train_micro_f1 = []

    Val_acc = []
    Val_loss = []
    Val_macro_f1 = []
    Val_micro_f1 = []

    def train(num_epochs, model, train_loader, val_loader, optimizer, criterion, model_save):
        epoch_trained = 0
        train_label_all = []
        train_predict_all = []
        val_label_all = []
        val_predict_all = []
        best_valid_loss = float('inf')

        start_time = time.time()
        for epoch in range(num_epochs):
            epoch_trained += 1
            epoch_start_time = time.time()
            # Training
            model.train()
            train_loss = 0.0
            train_accuracy = 0.0
            for statements, label_onehot, label, metadata_text, metadata_number in train_loader:
                statements = statements.to(DEVICE)
                label_onehot = label_onehot.to(DEVICE)
                label = label.to(DEVICE)
                metadata_text = metadata_text.to(DEVICE)
                metadata_number = metadata_number.to(DEVICE)

                optimizer.zero_grad()
                outputs = model(statements, metadata_text, metadata_number)
                loss = criterion(outputs, label_onehot)
                loss.backward()
                optimizer.step()

                train_loss += loss.item()
                _, train_predicted = torch.max(outputs, 1)
                train_accuracy += sum(train_predicted == label)
                train_predict_all += train_predicted.tolist()
                train_label_all += label.tolist()
            train_loss /= len(train_loader)
            train_accuracy /= len(train_loader.dataset)
            train_macro_f1 = f1_score(train_label_all, train_predict_all, average='macro')
            train_micro_f1 = f1_score(train_label_all, train_predict_all, average='micro')

            Train_acc.append(train_accuracy.tolist())
            Train_loss.append(train_loss)
            Train_macro_f1.append(train_macro_f1)
            Train_micro_f1.append(train_micro_f1)

            # Validation
            model.eval()
            val_loss = 0.0
            val_accuracy = 0.0
            with torch.no_grad():
                for statements, label_onehot, label, metadata_text, metadata_number in val_loader:
                    statements = statements.to(DEVICE)
                    label_onehot = label_onehot.to(DEVICE)
                    label = label.to(DEVICE)
                    metadata_text = metadata_text.to(DEVICE)
                    metadata_number = metadata_number.to(DEVICE)

                    val_outputs = model(statements, metadata_text, metadata_number)
                    val_loss += criterion(val_outputs, label_onehot).item()
                    _, val_predicted = torch.max(val_outputs, 1)
                    val_accuracy += sum(val_predicted == label)
                    val_predict_all += val_predicted.tolist()
                    val_label_all += label.tolist()
            val_loss /= len(val_loader)
            val_accuracy /= len(val_loader.dataset)
            val_macro_f1 = f1_score(val_label_all, val_predict_all, average='macro')
            val_micro_f1 = f1_score(val_label_all, val_predict_all, average='micro')

            Val_acc.append(val_accuracy.tolist())
            Val_loss.append(val_loss)
            Val_macro_f1.append(val_macro_f1)
            Val_micro_f1.append(val_micro_f1)

            if val_loss < best_valid_loss:
                best_valid_loss = val_loss
                torch.save(model.state_dict(), model_save)
                print(f'***** Best Result Updated at Epoch {epoch_trained}, Val Loss: {val_loss:.4f} *****')

            # Print the losses and accuracy
            epoch_end_time = time.time()
            epoch_time = epoch_end_time - epoch_start_time

            print(f"Epoch [{epoch+1}/{num_epochs}], Time: {epoch_time:.2f}s, Train Loss: {train_loss:.4f}, Train Acc: {train_accuracy:.4f}, Train F1 Macro: {train_macro_f1:.4f}, Train F1 Micro: {train_micro_f1:.4f}, Val Loss: {val_loss:.4f}, Val Acc: {val_accuracy:.4f}, Val F1 Macro: {val_macro_f1:.4f}, Val F1 Micro: {val_micro_f1:.4f}")

        end_time = time.time()
        training_time = end_time - start_time
        print(f'Total Training Time: {training_time:.2f}s')


    train(num_epochs, model, train_loader, val_loader, optimizer, criterion, model_save)


    # Evaluate the model on new data
    def test(model, test_loader, model_save):
        model.load_state_dict(torch.load(model_save))
        model.eval()

        test_label_all = []
        test_predict_all = []

        test_loss = 0.0
        test_accuracy = 0.0
        with torch.no_grad():
            for statements, label_onehot, label, metadata_text, metadata_number in test_loader:
                statements = statements.to(DEVICE)
                label_onehot = label_onehot.to(DEVICE)
                label = label.to(DEVICE)
                metadata_text = metadata_text.to(DEVICE)
                metadata_number = metadata_number.to(DEVICE)

                test_outputs = model(statements, metadata_text, metadata_number)
                test_loss += criterion(test_outputs, label_onehot).item()
                _, test_predicted = torch.max(test_outputs, 1)
                
                test_accuracy += sum(test_predicted == label)
                test_predict_all += test_predicted.tolist()
                test_label_all += label.tolist()

        test_loss /= len(test_loader)
        test_accuracy /= len(test_loader.dataset)
        test_macro_f1 = f1_score(test_label_all, test_predict_all, average='macro')
        test_micro_f1 = f1_score(test_label_all, test_predict_all, average='micro')

        print(f'Test Loss: {test_loss:.4f}, Test Acc: {test_accuracy:.4f}, Test F1 Macro: {test_macro_f1:.4f}, Test F1 Micro: {test_micro_f1:.4f}')


    test(model, test_loader, model_save)

torch.cuda.empty_cache()

class FuzzyLayer(nn.Module):
    def __init__(self, input_dim, membership_num):
        super(FuzzyLayer, self).__init__()

        # input_dim: feature number of the dataset
        # membership_num: number of membership function, also known as the class number

        self.input_dim = input_dim
        self.membership_num = membership_num

        self.membership_miu = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)
        self.membership_sigma = nn.Parameter(torch.Tensor(self.membership_num, self.input_dim).to(DEVICE), requires_grad=True)

        nn.init.xavier_uniform_(self.membership_miu)
        nn.init.ones_(self.membership_sigma)

    def forward(self, input_seq):
        batch_size = input_seq.size()[0]
        input_seq_exp = input_seq.unsqueeze(1).expand(batch_size, self.membership_num, self.input_dim)
        membership_miu_exp = self.membership_miu.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)
        membership_sigma_exp = self.membership_sigma.unsqueeze(0).expand(batch_size, self.membership_num, self.input_dim)

        fuzzy_membership = torch.mean(torch.exp((-1 / 2) * ((input_seq_exp - membership_miu_exp) / membership_sigma_exp) ** 2), dim=-1)
        # print(fuzzy_membership.shape)  #torch.Size([32, 6])
        return fuzzy_membership

## senti batch_size = 128
class SentAttNet(nn.Module):
    def __init__(self,  hidden_size=50):
        super(SentAttNet, self).__init__()
        self.sent_weight = nn.Parameter(torch.Tensor(2 * hidden_size, 10))
        self.sent_bias = nn.Parameter(torch.Tensor(10))
        self.context_weight = nn.Parameter(torch.Tensor(10, 1))
        self.gru1 = nn.GRU(510, hidden_size, bidirectional=True)  #将GRU改成LSTM
        self.gru2 = nn.GRU(509, hidden_size, bidirectional=True)
        self.gru3 = nn.GRU(508, hidden_size, bidirectional=True)
        self.gru4 = nn.GRU(58, hidden_size, bidirectional=True)
        self.gru5 = nn.GRU(57, hidden_size, bidirectional=True)
        self.gru6 = nn.GRU(56, hidden_size, bidirectional=True)
        self._create_weights(mean=0.0, std=0.05)
        self.hidden_size = hidden_size
    def _create_weights(self, mean=0.0, std=0.05):
        self.sent_weight.data.normal_(mean, std)
        self.context_weight.data.normal_(mean, std)

    def forward(self, input, hidden_state):
#         f_output, h_output = self.gru(input, hidden_state)
        input.to('cuda')
        hidden_state.to('cuda')
        input_shape = input.size()
        if(input_shape[-1]==510):
            f_output, h_output = self.gru1(input, hidden_state)
        elif(input_shape[-1]==509):
            f_output, h_output = self.gru2(input, hidden_state)
        elif(input_shape[-1]==508):
            f_output, h_output = self.gru3(input, hidden_state)
#         elif(input_shape[-1]==58):
#             f_output, h_output = self.gru4(input, hidden_state)
#         elif(input_shape[-1]==57):
#             f_output, h_output = self.gru5(input, hidden_state)
#         else:
# #             print(input_shape)
#             f_output, h_output = self.gru6(input, hidden_state)
        output = matrix_mul(f_output, self.sent_weight, self.context_weight, self.sent_bias)
        output = F.softmax(output)
        output = element_wise_mul(output.permute(1,0,2), f_output.permute(1,0,2))

        return output, h_output
    
def element_wise_mul(input1, input2):
    feature_list = []
    for feature_1, feature_2 in zip(input1, input2):
        feature = (feature_1 * feature_2).sum(dim=0)
        feature_list.append(feature)

    output = torch.stack(feature_list, dim=0)
    return output

def matrix_mul(input, weight, context_weight,  bias=False):
    input = torch.matmul(input, weight)
    input = input + bias
    input = torch.tanh(input)
    input = torch.matmul(input, context_weight)

    return input

class TextCNN(nn.Module):
    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, pad_idx):
        super().__init__()

        self.embedding = nn.Embedding(vocab_size, embedding_dim, padding_idx = pad_idx)
        self.convs = nn.ModuleList([
                                    nn.Conv1d(in_channels = embedding_dim, 
                                              out_channels = n_filters, 
                                              kernel_size = fs)
                                    for fs in filter_sizes
                                    ])      
        
        self.fc = nn.Linear(len(filter_sizes) * n_filters, output_dim)
        self.dropout = nn.Dropout(dropout)
        self.batch_size = 128
        self.sent_hidden_size = 50
        self._init_hidden_state()
        self.sent = SentAttNet( hidden_size=50)
        
    def _init_hidden_state(self, last_batch_size=None):
        if last_batch_size:
            batch_size = last_batch_size
        else:
            batch_size = self.batch_size
        self.sent_hidden_state = torch.zeros(2, batch_size, self.sent_hidden_size)
        if torch.cuda.is_available():
            self.sent_hidden_state = self.sent_hidden_state.cuda()
    
    def forward(self, text):
        #text = [batch size, sent len]
        embedded = self.embedding(text)
        #embedded = [batch size, sent len, emb dim]

        embedded = embedded.permute(0, 2, 1)
        #embedded = [batch size, emb dim, sent len]
#         print("embedded:",embedded.shape)
        conved = [F.relu(conv(embedded)) for conv in self.convs]
        #conved_n = [batch size, n_filters, sent len - filter_sizes[n] + 1]
#          因为conved是list，GRU因为时间步需要tensor
#         for i in range(len(conved)):
#             print("i:",conved[i].shape)

        # conv1 = []
        # for conv in conved:
        #     conv , self.sent_hidden_state= self.sent(conv , self.sent_hidden_state)
        #     conv1.append(conv)
        
        pooled = [F.max_pool1d(conv, conv.shape[2]).squeeze(2) for conv in conved]
        #pooled_n = [batch size, n_filters]
        cat = self.dropout(torch.cat(pooled, dim = 1))
        #cat = [batch size, n_filters * len(filter_sizes)]

        return self.fc(cat)

class CNNBiLSTM(nn.Module):
    def __init__(self, input_dim, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout):
        super().__init__()

        self.embedding = nn.Linear(input_dim, embedding_dim)
        self.conv = nn.Conv1d(in_channels=embedding_dim, out_channels=32, kernel_size=1)
        self.rnn = nn.LSTM(32, 
                           hidden_dim, 
                           num_layers=n_layers, 
                           bidirectional=bidirectional, 
                           dropout=dropout)
        self.fc = nn.Linear(hidden_dim * 2, output_dim)
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, metadata):
        #metadata = [batch size, metadata dim]

        embedded = self.dropout(self.embedding(metadata))
        #embedded = [batch size, metadata dim, emb dim]

        embedded = torch.reshape(embedded, (metadata.size(0), 128, 1))

        conved = F.relu(self.conv(embedded))
        #conved = [batch size, n_filters, metadata dim - filter_sizes[n] + 1]
        conved = torch.reshape(conved, (metadata.size(0), 32))
        outputs, (hidden, cell) = self.rnn(conved)
        
        #outputs = [metadata dim - filter_sizes[n] + 1, batch size, hid dim * num directions]
        #hidden = [num layers * num directions, batch size, hid dim]
        #cell = [num layers * num directions, batch size, hid dim]

        #concat the final forward (hidden[-2,:,:]) and backward (hidden[-1,:,:]) hidden layers
        #and apply dropout
        # hidden = self.dropout(torch.cat((hidden[-1,:], hidden[0,:]), dim = -1))
        #hidden = [batch size, hid dim * num directions]

        return self.fc(outputs)


class LiarModel(nn.Module):
    def __init__(self, vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx, input_dim, input_dim_metadata, hidden_dim, n_layers, bidirectional):
        super().__init__()

        self.textcnn = TextCNN(vocab_size, embedding_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)
        self.textcnn2 = TextCNN(vocab_size, input_dim, n_filters, filter_sizes, output_dim, dropout, padding_idx)
        self.cnn_bilstm = CNNBiLSTM(input_dim_metadata, embedding_dim, hidden_dim, output_dim, n_layers, bidirectional, dropout)
        self.fuzzy = FuzzyLayer(output_dim, output_dim)
        self.fuse = nn.Linear(output_dim * 4, output_dim)
    
    def forward(self, text, metadata_text, metadata_number):
        #text = [batch size, sent len]
        #metadata = [batch size, metadata dim]

        text_output = self.textcnn(text)
        metadata_output_text = self.textcnn2(metadata_text)
        metadata_output_number = self.cnn_bilstm(metadata_number)
        metadata_output_fuzzy = self.fuzzy(metadata_output_number)

        fused_output = self.fuse(torch.cat((text_output, metadata_output_text, metadata_output_number, metadata_output_fuzzy), dim=1))

        return fused_output



